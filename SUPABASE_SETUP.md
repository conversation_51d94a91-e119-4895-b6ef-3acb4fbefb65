# Supabase Setup Guide for Prompt Manager

This guide will help you set up Supabase for the Prompt Manager application.

## Prerequisites

1. A Supabase account (sign up at [supabase.com](https://supabase.com))
2. Node.js and pnpm installed

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `prompt-manager`
   - **Database Password**: Choose a strong password (save this!)
   - **Region**: Choose the closest region to your users
5. Click "Create new project"
6. Wait for the project to be created (this may take a few minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-ref.supabase.co`)
   - **anon public** key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)
   - **service_role** key (also starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

## Step 3: Configure Environment Variables

1. In your project root, update the `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

Replace the placeholder values with your actual Supabase credentials.

## Step 4: Set Up the Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `database/schema.sql` from this project
3. Paste it into the SQL Editor
4. Click "Run" to execute the schema

This will create:
- `prompts` table for storing user prompts
- `folders` table for organizing prompts
- `prompt_folders` table for many-to-many relationships
- Row Level Security (RLS) policies to ensure data privacy
- Indexes for better performance
- Triggers for automatic timestamp updates

## Step 5: Configure Authentication

1. In your Supabase dashboard, go to **Authentication** > **Settings**
2. Configure the following:

### Email Settings
- **Enable email confirmations**: Turn this ON for production, OFF for development
- **Enable email change confirmations**: Turn this ON

### OAuth Providers (Optional)
To enable GitHub login:
1. Go to **Authentication** > **Providers**
2. Enable **GitHub**
3. Add your GitHub OAuth app credentials:
   - **Client ID**: Your GitHub OAuth app client ID
   - **Client Secret**: Your GitHub OAuth app client secret
   - **Redirect URL**: `https://your-project-ref.supabase.co/auth/v1/callback`

### Site URL
- Set your site URL to `http://localhost:3000` for development
- For production, set it to your actual domain

## Step 6: Test the Setup

1. Start your development server:
   ```bash
   pnpm dev
   ```

2. Open [http://localhost:3000](http://localhost:3000)
3. You should be redirected to the login page
4. Try creating an account and logging in
5. Once logged in, you should see the prompt manager interface

## Step 7: Add Sample Data (Optional)

After creating your first user account, you can add some sample data:

1. Go to **SQL Editor** in Supabase
2. Run the following SQL (uncomment the sample data section in `schema.sql`):

```sql
-- Replace 'your-user-id' with your actual user ID from auth.users table
INSERT INTO public.prompts (title, content, category, tags, rating, is_pinned, user_id) VALUES
('Code Review Assistant', 'You are an expert code reviewer. Please review the following code and provide constructive feedback...', 'development', ARRAY['code', 'review', 'programming'], 5, true, 'your-user-id'),
('Email Writer', 'Help me write a professional email for the following situation...', 'communication', ARRAY['email', 'professional', 'writing'], 4, false, 'your-user-id');

INSERT INTO public.folders (name, color, user_id) VALUES
('Development', 'bg-blue-500', 'your-user-id'),
('Communication', 'bg-green-500', 'your-user-id');
```

## Troubleshooting

### Common Issues

1. **"Invalid API key"**: Double-check your environment variables
2. **"Row Level Security policy violation"**: Make sure RLS policies are set up correctly
3. **"User not authenticated"**: Check that authentication is working properly

### Getting User ID

To find your user ID for sample data:
1. Go to **Authentication** > **Users** in Supabase dashboard
2. Find your user and copy the ID

### Checking Logs

- Go to **Logs** in your Supabase dashboard to see real-time logs
- Check the browser console for client-side errors

## Security Notes

- Never commit your `.env.local` file to version control
- The `anon` key is safe to use in client-side code
- The `service_role` key should only be used server-side
- RLS policies ensure users can only access their own data
- Always test your RLS policies before going to production

## Next Steps

Once everything is working:
1. Customize the prompt categories and tags for your use case
2. Add more features like prompt sharing, templates, etc.
3. Deploy to production (Vercel, Netlify, etc.)
4. Update your Supabase site URL to your production domain
