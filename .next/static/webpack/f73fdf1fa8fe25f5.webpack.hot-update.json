{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/page.tsx", "(app-pages-browser)/./components/ui/badge.tsx", "(app-pages-browser)/./components/ui/dialog.tsx", "(app-pages-browser)/./components/ui/select.tsx", "(app-pages-browser)/./components/ui/textarea.tsx", "(app-pages-browser)/./lib/database.ts", "(app-pages-browser)/./node_modules/.pnpm/@floating-ui+core@1.7.1/node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "(app-pages-browser)/./node_modules/.pnpm/@floating-ui+dom@1.7.1/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "(app-pages-browser)/./node_modules/.pnpm/@floating-ui+react-dom@2.1.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "(app-pages-browser)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "(app-pages-browser)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-arrow/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.14_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-dialog/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@19.0.0_@types+react@19.0.0_re_b59f4b41d800b5f0086bce896090272d/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom_de0cecae7b4b782a7da1531aaeae0ba9/node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-id/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-popper/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-presence/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@19.0.0_@types+react@19.0.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@radix-ui/react-select/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-effect-event@0.0.2_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-effect-event/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-escape-keydown@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-previous@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-use-size@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-use-size/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@19.0.0_@types+react@19.0.0_react_b4f82397182c21a3654af937aa366655/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_check_private_redeclaration.js", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_private_field_get.js", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_private_field_init.js", "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_private_field_set.js", "(app-pages-browser)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js", "(app-pages-browser)/./node_modules/.pnpm/get-nonce@1.0.1/node_modules/get-nonce/dist/es2015/index.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/folder.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/grid-2x2.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/grid-3x3.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/layout-grid.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/list.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js", "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftommylee%2FDesktop%2Fexperiment%2FPropmt%20manager%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll-bar@2.3.8_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll-bar/dist/es2015/component.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll-bar@2.3.8_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll-bar@2.3.8_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll-bar/dist/es2015/index.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll-bar@2.3.8_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.0.0_react@19.0.0/node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/.pnpm/react-style-singleton@2.2.3_@types+react@19.0.0_react@19.0.0/node_modules/react-style-singleton/dist/es2015/component.js", "(app-pages-browser)/./node_modules/.pnpm/react-style-singleton@2.2.3_@types+react@19.0.0_react@19.0.0/node_modules/react-style-singleton/dist/es2015/hook.js", "(app-pages-browser)/./node_modules/.pnpm/react-style-singleton@2.2.3_@types+react@19.0.0_react@19.0.0/node_modules/react-style-singleton/dist/es2015/index.js", "(app-pages-browser)/./node_modules/.pnpm/react-style-singleton@2.2.3_@types+react@19.0.0_react@19.0.0/node_modules/react-style-singleton/dist/es2015/singleton.js", "(app-pages-browser)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@19.0.0_react@19.0.0/node_modules/use-callback-ref/dist/es2015/assignRef.js", "(app-pages-browser)/./node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@19.0.0_react@19.0.0/node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "(app-pages-browser)/./node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@19.0.0_react@19.0.0/node_modules/use-callback-ref/dist/es2015/useRef.js", "(app-pages-browser)/./node_modules/.pnpm/use-sidecar@1.1.3_@types+react@19.0.0_react@19.0.0/node_modules/use-sidecar/dist/es2015/exports.js", "(app-pages-browser)/./node_modules/.pnpm/use-sidecar@1.1.3_@types+react@19.0.0_react@19.0.0/node_modules/use-sidecar/dist/es2015/medium.js"]}