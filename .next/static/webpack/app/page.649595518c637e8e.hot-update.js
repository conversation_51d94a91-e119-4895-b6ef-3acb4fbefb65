"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true\n    });\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            setPrompts(data);\n            addToLog(\"loaded \".concat(data.length, \" prompts\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: \"general\",\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = (prompt)=>{\n        var _prompt_folderInfo;\n        // Add current folder info to editing prompt\n        const promptWithFolder = {\n            ...prompt,\n            currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n        };\n        setEditingPrompt(promptWithFolder);\n        setIsEditDialogOpen(true);\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                }\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 522,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.category,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        category: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select category\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 528,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 527,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"general\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"General\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 531,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"development\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Development\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 532,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"communication\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Communication\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 533,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"analysis\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Analysis\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 534,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"creative\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Creative\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 535,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 530,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 523,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 540,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 546,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 545,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 549,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 551,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 548,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 541,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 580,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 589,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 621,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 627,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 626,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 630,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 631,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 632,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 633,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 634,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 629,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 622,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 639,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 645,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 644,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 648,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 650,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 647,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 640,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 638,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 680,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 681,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 690,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 696,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 695,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 699,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 700,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 701,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 702,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 703,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 698,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 691,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 689,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 709,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 716,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 722,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 724,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 725,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 726,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 723,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 721,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 731,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 742,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 730,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                children: \"+ New Prompt\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 851,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 853,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600\")\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 856,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 854,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 887,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 901,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 903,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 921,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 409,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"iO6LDviIJFhRZXK/m6AiZmXShjU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});