"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            setPrompts(data);\n            addToLog(\"loaded \".concat(data.length, \" prompts\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // For now, we'll just use category filtering since we don't have folder relationships implemented yet\n        // TODO: Add folder filtering when prompt_folders relationship is implemented\n        return matchesSearch && matchesCategory;\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: newPrompt.tags,\n                user_id: user.id\n            });\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: \"general\",\n                tags: [],\n                folderId: \"\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 376,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 382,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                    value: newPrompt.category,\n                                                                                    onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                category: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                            className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                placeholder: \"Select category\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 393,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 392,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                            className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"general\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"General\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 396,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"development\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Development\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 397,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"communication\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Communication\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 398,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"analysis\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Analysis\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 399,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"creative\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Creative\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 400,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 395,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                    value: newPrompt.folderId,\n                                                                                    onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                folderId: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                            className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                placeholder: \"Select folder (optional)\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 408,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 407,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                            className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"none\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"No folder\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 411,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: folder.id,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: folder.name\n                                                                                                    }, folder.id, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 413,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 410,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 403,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 423,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 441,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 444,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600\")\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 519,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 517,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-green-500 border-green-600\",\n                                                                        children: tag\n                                                                    }, tag, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"OUhdpagacG+J4LS+4in6/sJApqo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});