"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"smart\") // smart, date, title, rating\n    ;\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true,\n        customCategories: []\n    });\n    const [newCustomCategory, setNewCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddingCustomCategory, setIsAddingCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customCategoryInput, setCustomCategoryInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [draggedPrompt, setDraggedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    // Get all available categories (default + custom)\n    const allCategories = [\n        \"general\",\n        \"development\",\n        \"communication\",\n        \"analysis\",\n        \"creative\",\n        ...userSettings.customCategories\n    ];\n    // Advanced sorting function\n    const sortPrompts = (prompts)=>{\n        return prompts.sort((a, b)=>{\n            // 1. Always prioritize pinned prompts\n            if (a.is_pinned && !b.is_pinned) return -1;\n            if (!a.is_pinned && b.is_pinned) return 1;\n            // 2. Apply secondary sorting\n            switch(sortBy){\n                case \"smart\":\n                    // Smart: Pin > Rating > Recent\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"rating\":\n                    // Rating: Pin > Rating > Title\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return a.title.localeCompare(b.title);\n                case \"date\":\n                    // Date: Pin > Recent > Old\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"title\":\n                    // Title: Pin > Alphabetical\n                    return a.title.localeCompare(b.title);\n                default:\n                    return 0;\n            }\n        });\n    };\n    const filteredPrompts = sortPrompts(prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }));\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const addCustomCategory = (categoryName)=>{\n        const category = categoryName.trim().toLowerCase();\n        if (category && !allCategories.includes(category)) {\n            // Add to user settings\n            const updatedSettings = {\n                ...userSettings,\n                customCategories: [\n                    ...userSettings.customCategories,\n                    category\n                ]\n            };\n            setUserSettings(updatedSettings);\n            // Save to localStorage immediately\n            localStorage.setItem('promptManagerSettings', JSON.stringify(updatedSettings));\n            addToLog('added custom category \"'.concat(category, '\"'));\n            return category;\n        }\n        return null;\n    };\n    const movePromptToFolder = async (promptId, targetFolderId)=>{\n        if (!user) return;\n        try {\n            var _prompt_folderInfo, _folders_find;\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const currentFolderId = ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || null;\n            // Don't do anything if dropping in the same folder\n            if (currentFolderId === targetFolderId) return;\n            // Update database relationship\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(promptId, currentFolderId, targetFolderId);\n            // Update local state\n            setPrompts((prev)=>prev.map((p)=>{\n                    if (p.id === promptId) {\n                        const updatedPrompt = {\n                            ...p\n                        };\n                        if (targetFolderId) {\n                            const targetFolder = folders.find((f)=>f.id === targetFolderId);\n                            if (targetFolder) {\n                                updatedPrompt.folderInfo = {\n                                    id: targetFolder.id,\n                                    name: targetFolder.name,\n                                    color: targetFolder.color\n                                };\n                            }\n                        } else {\n                            delete updatedPrompt.folderInfo;\n                        }\n                        return updatedPrompt;\n                    }\n                    return p;\n                }));\n            const targetFolderName = targetFolderId ? ((_folders_find = folders.find((f)=>f.id === targetFolderId)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"folder\" : \"no folder\";\n            addToLog('moved \"'.concat(prompt.title, '\" to ').concat(targetFolderName));\n        } catch (err) {\n            addToLog(\"failed to move prompt: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsAddingCustomCategory(false);\n            setCustomCategoryInput(\"\");\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity\",\n                                            onClick: ()=>{\n                                                setSelectedFolder(null);\n                                                setSearchTerm(\"\");\n                                                setSelectedCategory(\"all\");\n                                                setSortBy(\"smart\");\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-black text-2xl terminal-glow select-none\",\n                                                    children: [\n                                                        \">\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"_\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                                    children: \"PROMPT MANAGER\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === 'all' ? 'bg-green-800/30 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder('all');\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, null);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: (open)=>{\n                                                                setIsNewPromptDialogOpen(open);\n                                                                if (!open) {\n                                                                    setIsAddingCustomCategory(false);\n                                                                    setCustomCategoryInput(\"\");\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 778,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 785,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 784,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 788,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 794,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 801,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        !isAddingCustomCategory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                                value: newPrompt.category,\n                                                                                                onValueChange: (value)=>{\n                                                                                                    if (value === \"add_custom\") {\n                                                                                                        setIsAddingCustomCategory(true);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    } else {\n                                                                                                        setNewPrompt((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                category: value\n                                                                                                            }));\n                                                                                                    }\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                            placeholder: \"Select category\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 816,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 815,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                        children: [\n                                                                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                    value: category,\n                                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                                }, category, false, {\n                                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                    lineNumber: 820,\n                                                                                                                    columnNumber: 39\n                                                                                                                }, this)),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: \"add_custom\",\n                                                                                                                className: \"text-blue-400 hover:bg-gray-700 font-semibold\",\n                                                                                                                children: \"+ Add Custom Category\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 824,\n                                                                                                                columnNumber: 37\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 818,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 804,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 803,\n                                                                                            columnNumber: 31\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                    placeholder: \"Enter custom category...\",\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                    value: customCategoryInput,\n                                                                                                    onChange: (e)=>setCustomCategoryInput(e.target.value),\n                                                                                                    onKeyPress: (e)=>{\n                                                                                                        if (e.key === 'Enter' && customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    autoFocus: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 832,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    onClick: ()=>{\n                                                                                                        if (customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                    children: \"Add\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 849,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    variant: \"outline\",\n                                                                                                    onClick: ()=>{\n                                                                                                        setIsAddingCustomCategory(false);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    },\n                                                                                                    className: \"border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 865,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 831,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 800,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 880,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 886,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 885,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 889,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 891,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 888,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 881,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 879,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 902,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 787,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 914,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 920,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 923,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 929,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 943,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 954,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 961,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 967,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 966,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 971,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 969,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 962,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 979,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 985,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 984,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 988,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 990,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 987,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 980,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 978,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1001,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 947,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1016,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1015,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1020,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1021,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1019,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1030,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1036,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1035,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1040,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1038,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1031,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1049,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1056,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1048,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Custom Categories\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1062,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex space-x-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                        placeholder: \"Add custom category...\",\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        value: newCustomCategory,\n                                                                                                        onChange: (e)=>setNewCustomCategory(e.target.value),\n                                                                                                        onKeyPress: (e)=>{\n                                                                                                            if (e.key === 'Enter' && newCustomCategory.trim()) {\n                                                                                                                const category = newCustomCategory.trim().toLowerCase();\n                                                                                                                if (!allCategories.includes(category)) {\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: [\n                                                                                                                                ...prev.customCategories,\n                                                                                                                                category\n                                                                                                                            ]\n                                                                                                                        }));\n                                                                                                                    setNewCustomCategory(\"\");\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1065,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                        size: \"sm\",\n                                                                                                        onClick: ()=>{\n                                                                                                            const category = newCustomCategory.trim().toLowerCase();\n                                                                                                            if (category && !allCategories.includes(category)) {\n                                                                                                                setUserSettings((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        customCategories: [\n                                                                                                                            ...prev.customCategories,\n                                                                                                                            category\n                                                                                                                        ]\n                                                                                                                    }));\n                                                                                                                setNewCustomCategory(\"\");\n                                                                                                            }\n                                                                                                        },\n                                                                                                        className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                        children: \"Add\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1083,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1064,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            userSettings.customCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"space-y-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-green-500\",\n                                                                                                        children: \"Your custom categories:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1103,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                                        children: userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                                                                variant: \"outline\",\n                                                                                                                className: \"text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: prev.customCategories.filter((c)=>c !== category)\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: [\n                                                                                                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                                                                                                    \" \\xd7\"\n                                                                                                                ]\n                                                                                                            }, category, true, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 1106,\n                                                                                                                columnNumber: 39\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1104,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: \"Click to remove\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1121,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1102,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1063,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1061,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1128,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1130,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1131,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1132,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1129,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1127,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1137,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1148,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1136,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1018,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1013,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1225,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"general\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1226,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1229,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1230,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: category,\n                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                }, category, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1232,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: sortBy,\n                                                onValueChange: setSortBy,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-40 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1241,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"smart\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Smart Sort\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"rating\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"date\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1246,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"title\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1256,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1255,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1268,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1267,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1260,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                draggable: true,\n                                                onDragStart: (e)=>{\n                                                    setDraggedPrompt(prompt);\n                                                    e.dataTransfer.effectAllowed = 'move';\n                                                    e.dataTransfer.setData('text/plain', prompt.id);\n                                                    // Add visual feedback\n                                                    e.currentTarget.style.opacity = '0.5';\n                                                },\n                                                onDragEnd: (e)=>{\n                                                    setDraggedPrompt(null);\n                                                    setDragOverFolder(null);\n                                                    e.currentTarget.style.opacity = '1';\n                                                },\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1299,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1301,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1304,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1302,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1300,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1298,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1325,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1329,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1334,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1344,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1353,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1347,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1361,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1355,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1369,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1363,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 1169,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1384,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1386,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1391,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1392,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1390,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1382,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1381,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 656,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 654,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 653,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"N0Z76MMChFCD+V1lCWDLl7qRtJQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});