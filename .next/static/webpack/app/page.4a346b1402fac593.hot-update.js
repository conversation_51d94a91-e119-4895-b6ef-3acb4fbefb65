"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"smart\") // smart, date, title, rating\n    ;\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true,\n        customCategories: []\n    });\n    const [newCustomCategory, setNewCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddingCustomCategory, setIsAddingCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customCategoryInput, setCustomCategoryInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [draggedPrompt, setDraggedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTrashOpen, setIsTrashOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOverTrash, setDragOverTrash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [layoutMode, setLayoutMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    // Get all available categories (default + custom)\n    const allCategories = [\n        \"general\",\n        \"development\",\n        \"communication\",\n        \"analysis\",\n        \"creative\",\n        ...userSettings.customCategories\n    ];\n    // Advanced sorting function\n    const sortPrompts = (prompts)=>{\n        return prompts.sort((a, b)=>{\n            // 1. Always prioritize pinned prompts\n            if (a.is_pinned && !b.is_pinned) return -1;\n            if (!a.is_pinned && b.is_pinned) return 1;\n            // 2. Apply secondary sorting\n            switch(sortBy){\n                case \"smart\":\n                    // Smart: Pin > Rating > Recent\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"rating\":\n                    // Rating: Pin > Rating > Title\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return a.title.localeCompare(b.title);\n                case \"date\":\n                    // Date: Pin > Recent > Old\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"title\":\n                    // Title: Pin > Alphabetical\n                    return a.title.localeCompare(b.title);\n                default:\n                    return 0;\n            }\n        });\n    };\n    const filteredPrompts = sortPrompts(prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }));\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const addCustomCategory = (categoryName)=>{\n        const category = categoryName.trim().toLowerCase();\n        if (category && !allCategories.includes(category)) {\n            // Add to user settings\n            const updatedSettings = {\n                ...userSettings,\n                customCategories: [\n                    ...userSettings.customCategories,\n                    category\n                ]\n            };\n            setUserSettings(updatedSettings);\n            // Save to localStorage immediately\n            localStorage.setItem('promptManagerSettings', JSON.stringify(updatedSettings));\n            addToLog('added custom category \"'.concat(category, '\"'));\n            return category;\n        }\n        return null;\n    };\n    const movePromptToFolder = async (promptId, targetFolderId)=>{\n        if (!user) return;\n        try {\n            var _prompt_folderInfo, _folders_find;\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const currentFolderId = ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || null;\n            // Don't do anything if dropping in the same folder\n            if (currentFolderId === targetFolderId) return;\n            // Update database relationship\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(promptId, currentFolderId, targetFolderId);\n            // Update local state\n            setPrompts((prev)=>prev.map((p)=>{\n                    if (p.id === promptId) {\n                        const updatedPrompt = {\n                            ...p\n                        };\n                        if (targetFolderId) {\n                            const targetFolder = folders.find((f)=>f.id === targetFolderId);\n                            if (targetFolder) {\n                                updatedPrompt.folderInfo = {\n                                    id: targetFolder.id,\n                                    name: targetFolder.name,\n                                    color: targetFolder.color\n                                };\n                            }\n                        } else {\n                            delete updatedPrompt.folderInfo;\n                        }\n                        return updatedPrompt;\n                    }\n                    return p;\n                }));\n            const targetFolderName = targetFolderId ? ((_folders_find = folders.find((f)=>f.id === targetFolderId)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"folder\" : \"no folder\";\n            addToLog('moved \"'.concat(prompt.title, '\" to ').concat(targetFolderName));\n        } catch (err) {\n            addToLog(\"failed to move prompt: \".concat(err.message));\n        }\n    };\n    const deletePrompt = async (promptId)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            // Delete from database\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.delete(promptId, user.id);\n            // Remove from local state\n            setPrompts((prev)=>prev.filter((p)=>p.id !== promptId));\n            addToLog('deleted prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to delete prompt: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsAddingCustomCategory(false);\n            setCustomCategoryInput(\"\");\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity\",\n                                            onClick: ()=>{\n                                                setSelectedFolder(null);\n                                                setSearchTerm(\"\");\n                                                setSelectedCategory(\"all\");\n                                                setSortBy(\"smart\");\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-black text-2xl terminal-glow select-none\",\n                                                    children: [\n                                                        \">\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"_\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                                    children: \"PROMPT MANAGER\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === 'all' ? 'bg-green-800/30 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder('all');\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, null);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === folder.id ? 'bg-green-800/30 border-green-400 border' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                onDragOver: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.dataTransfer.dropEffect = 'move';\n                                                                    setDragOverFolder(folder.id);\n                                                                },\n                                                                onDragLeave: ()=>setDragOverFolder(null),\n                                                                onDrop: (e)=>{\n                                                                    e.preventDefault();\n                                                                    if (draggedPrompt) {\n                                                                        movePromptToFolder(draggedPrompt.id, folder.id);\n                                                                    }\n                                                                    setDragOverFolder(null);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: (open)=>{\n                                                                setIsNewPromptDialogOpen(open);\n                                                                if (!open) {\n                                                                    setIsAddingCustomCategory(false);\n                                                                    setCustomCategoryInput(\"\");\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 814,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 820,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 823,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 836,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        !isAddingCustomCategory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                                value: newPrompt.category,\n                                                                                                onValueChange: (value)=>{\n                                                                                                    if (value === \"add_custom\") {\n                                                                                                        setIsAddingCustomCategory(true);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    } else {\n                                                                                                        setNewPrompt((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                category: value\n                                                                                                            }));\n                                                                                                    }\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                            placeholder: \"Select category\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 851,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 850,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                        children: [\n                                                                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                    value: category,\n                                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                                }, category, false, {\n                                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                    lineNumber: 855,\n                                                                                                                    columnNumber: 39\n                                                                                                                }, this)),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: \"add_custom\",\n                                                                                                                className: \"text-blue-400 hover:bg-gray-700 font-semibold\",\n                                                                                                                children: \"+ Add Custom Category\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 859,\n                                                                                                                columnNumber: 37\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 853,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 839,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 838,\n                                                                                            columnNumber: 31\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                    placeholder: \"Enter custom category...\",\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                    value: customCategoryInput,\n                                                                                                    onChange: (e)=>setCustomCategoryInput(e.target.value),\n                                                                                                    onKeyPress: (e)=>{\n                                                                                                        if (e.key === 'Enter' && customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    autoFocus: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 867,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    onClick: ()=>{\n                                                                                                        if (customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                    children: \"Add\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 884,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    variant: \"outline\",\n                                                                                                    onClick: ()=>{\n                                                                                                        setIsAddingCustomCategory(false);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    },\n                                                                                                    className: \"border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 900,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 866,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 835,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 915,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 921,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 920,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 924,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 926,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 923,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 916,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 914,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 937,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 822,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 949,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 948,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 955,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 958,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 964,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 979,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 983,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 989,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 996,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1002,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1001,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1006,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1004,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 997,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 995,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1014,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1020,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1019,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1023,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 1025,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1022,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1015,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1013,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1036,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 976,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1051,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1050,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1055,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1056,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1054,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1065,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1071,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1070,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1075,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1073,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1066,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1064,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1084,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1091,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1083,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Custom Categories\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1097,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex space-x-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                        placeholder: \"Add custom category...\",\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        value: newCustomCategory,\n                                                                                                        onChange: (e)=>setNewCustomCategory(e.target.value),\n                                                                                                        onKeyPress: (e)=>{\n                                                                                                            if (e.key === 'Enter' && newCustomCategory.trim()) {\n                                                                                                                const category = newCustomCategory.trim().toLowerCase();\n                                                                                                                if (!allCategories.includes(category)) {\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: [\n                                                                                                                                ...prev.customCategories,\n                                                                                                                                category\n                                                                                                                            ]\n                                                                                                                        }));\n                                                                                                                    setNewCustomCategory(\"\");\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1100,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                        size: \"sm\",\n                                                                                                        onClick: ()=>{\n                                                                                                            const category = newCustomCategory.trim().toLowerCase();\n                                                                                                            if (category && !allCategories.includes(category)) {\n                                                                                                                setUserSettings((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        customCategories: [\n                                                                                                                            ...prev.customCategories,\n                                                                                                                            category\n                                                                                                                        ]\n                                                                                                                    }));\n                                                                                                                setNewCustomCategory(\"\");\n                                                                                                            }\n                                                                                                        },\n                                                                                                        className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                        children: \"Add\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1118,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1099,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            userSettings.customCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"space-y-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-green-500\",\n                                                                                                        children: \"Your custom categories:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1138,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                                        children: userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                                                                variant: \"outline\",\n                                                                                                                className: \"text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: prev.customCategories.filter((c)=>c !== category)\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: [\n                                                                                                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                                                                                                    \" \\xd7\"\n                                                                                                                ]\n                                                                                                            }, category, true, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 1141,\n                                                                                                                columnNumber: 39\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1139,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: \"Click to remove\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1156,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1137,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1098,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1096,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1163,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1165,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1166,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1167,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1164,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1162,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1172,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1183,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1048,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1195,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> DELETE\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1203,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center p-4 rounded-lg border-2 border-dashed transition-all duration-300 \".concat(dragOverTrash ? 'border-red-500 bg-red-900/30 scale-110' : 'border-gray-600 hover:border-red-400'),\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        e.dataTransfer.dropEffect = 'move';\n                                                        setDragOverTrash(true);\n                                                        setIsTrashOpen(true);\n                                                    },\n                                                    onDragLeave: ()=>{\n                                                        setDragOverTrash(false);\n                                                        setIsTrashOpen(false);\n                                                    },\n                                                    onDrop: (e)=>{\n                                                        e.preventDefault();\n                                                        if (draggedPrompt) {\n                                                            deletePrompt(draggedPrompt.id);\n                                                        }\n                                                        setDragOverTrash(false);\n                                                        setIsTrashOpen(false);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"transition-all duration-300 \".concat(isTrashOpen || dragOverTrash ? 'scale-125 text-red-400' : 'text-gray-500'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-8 h-8 transition-all duration-300 \".concat(isTrashOpen || dragOverTrash ? 'animate-bounce' : '')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1232,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1229,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mt-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs transition-colors duration-300 \".concat(dragOverTrash ? 'text-red-400' : 'text-gray-500'),\n                                                        children: dragOverTrash ? 'Release to Delete' : 'Drop to Delete'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1280,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1302,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1305,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"general\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1306,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1307,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1308,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1309,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1310,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: category,\n                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                }, category, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: sortBy,\n                                                onValueChange: setSortBy,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-40 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1321,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"smart\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Smart Sort\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"rating\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1325,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"date\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"title\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1327,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1323,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1290,\n                                        columnNumber: 15\n                                    }, this),\n                                    folders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-semibold\",\n                                                    children: \"Quick Access Folders:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1335,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 \".concat(dragOverFolder === 'all' ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50', \" \").concat(selectedFolder === null ? 'bg-green-900/30 border-green-500' : ''),\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        onDragOver: (e)=>{\n                                                            e.preventDefault();\n                                                            e.dataTransfer.dropEffect = 'move';\n                                                            setDragOverFolder('all');\n                                                        },\n                                                        onDragLeave: ()=>setDragOverFolder(null),\n                                                        onDrop: (e)=>{\n                                                            e.preventDefault();\n                                                            if (draggedPrompt) {\n                                                                movePromptToFolder(draggedPrompt.id, null);\n                                                            }\n                                                            setDragOverFolder(null);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1360,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1359,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-300 text-center font-medium\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-500\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    prompts.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1340,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 \".concat(dragOverFolder === folder.id ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50', \" \").concat(selectedFolder === folder.id ? 'bg-green-900/30 border-green-500' : ''),\n                                                            onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder(folder.id);\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, folder.id);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 \".concat(folder.color, \" rounded-lg flex items-center justify-center mb-2\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1389,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1388,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-300 text-center font-medium max-w-16 truncate\",\n                                                                    children: folder.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1391,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        folder.promptCount || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1394,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, folder.id, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1368,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1338,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1334,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1405,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1404,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1409,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                draggable: true,\n                                                onDragStart: (e)=>{\n                                                    setDraggedPrompt(prompt);\n                                                    e.dataTransfer.effectAllowed = 'move';\n                                                    e.dataTransfer.setData('text/plain', prompt.id);\n                                                    // Add visual feedback\n                                                    e.currentTarget.style.opacity = '0.5';\n                                                },\n                                                onDragEnd: (e)=>{\n                                                    setDraggedPrompt(null);\n                                                    setDragOverFolder(null);\n                                                    setDragOverTrash(false);\n                                                    setIsTrashOpen(false);\n                                                    e.currentTarget.style.opacity = '1';\n                                                },\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 opacity-30 hover:opacity-60 transition-opacity\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-green-400 text-xs\",\n                                                                            children: \"⋮⋮\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1450,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1453,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1455,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1458,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1456,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1454,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1449,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1479,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1483,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1488,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1448,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1495,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1497,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1498,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1496,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1507,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1501,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1515,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1509,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1523,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1517,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1500,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1494,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1427,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1402,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: draggedPrompt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: [\n                                                    'Dragging \"',\n                                                    draggedPrompt.title,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1540,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: \"Drop on folder to move\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1542,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1543,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: \"Drop on trash to delete\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1544,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1548,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1549,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    filteredPrompts.length,\n                                                    \" prompts\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1550,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1551,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    folders.length,\n                                                    \" folders\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1537,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1558,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1536,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1535,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 676,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 675,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"necF87T3Iqs9Fm0xUr/Yq48jutM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});