"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true\n    });\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }).sort((a, b)=>{\n        // Sort by pinned status first (pinned items at top), then by creation date\n        if (a.is_pinned && !b.is_pinned) return -1;\n        if (!a.is_pinned && b.is_pinned) return 1;\n        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 638,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 647,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 654,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.category,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        category: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select category\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 660,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 659,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"general\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"General\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 663,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"development\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Development\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 664,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"communication\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Communication\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 665,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"analysis\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Analysis\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 666,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"creative\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Creative\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 667,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 662,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 655,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 672,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 678,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 677,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 681,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 683,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 680,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 673,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 694,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 721,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 740,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 753,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 759,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 758,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 762,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 763,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 764,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 765,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 766,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 761,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 754,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 752,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 771,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 777,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 776,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 780,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 782,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 779,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 772,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 812,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 813,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 811,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 822,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 828,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 827,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 831,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 832,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 833,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 834,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 835,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 830,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 823,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 821,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 841,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 848,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 854,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 856,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 857,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 858,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 855,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 853,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 863,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 874,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 953,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 964,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 994,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 996,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 999,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 997,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1024,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1029,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1019,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1038,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1039,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1048,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1042,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1056,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1050,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1064,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1041,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 961,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1082,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1087,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1077,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1076,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 544,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 542,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 541,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"iO6LDviIJFhRZXK/m6AiZmXShjU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});