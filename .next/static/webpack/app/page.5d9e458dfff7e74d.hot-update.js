"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"smart\") // smart, date, title, rating\n    ;\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true,\n        customCategories: []\n    });\n    const [newCustomCategory, setNewCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddingCustomCategory, setIsAddingCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customCategoryInput, setCustomCategoryInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [draggedPrompt, setDraggedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isTrashOpen, setIsTrashOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOverTrash, setDragOverTrash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    // Get all available categories (default + custom)\n    const allCategories = [\n        \"general\",\n        \"development\",\n        \"communication\",\n        \"analysis\",\n        \"creative\",\n        ...userSettings.customCategories\n    ];\n    // Advanced sorting function\n    const sortPrompts = (prompts)=>{\n        return prompts.sort((a, b)=>{\n            // 1. Always prioritize pinned prompts\n            if (a.is_pinned && !b.is_pinned) return -1;\n            if (!a.is_pinned && b.is_pinned) return 1;\n            // 2. Apply secondary sorting\n            switch(sortBy){\n                case \"smart\":\n                    // Smart: Pin > Rating > Recent\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"rating\":\n                    // Rating: Pin > Rating > Title\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return a.title.localeCompare(b.title);\n                case \"date\":\n                    // Date: Pin > Recent > Old\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"title\":\n                    // Title: Pin > Alphabetical\n                    return a.title.localeCompare(b.title);\n                default:\n                    return 0;\n            }\n        });\n    };\n    const filteredPrompts = sortPrompts(prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }));\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const addCustomCategory = (categoryName)=>{\n        const category = categoryName.trim().toLowerCase();\n        if (category && !allCategories.includes(category)) {\n            // Add to user settings\n            const updatedSettings = {\n                ...userSettings,\n                customCategories: [\n                    ...userSettings.customCategories,\n                    category\n                ]\n            };\n            setUserSettings(updatedSettings);\n            // Save to localStorage immediately\n            localStorage.setItem('promptManagerSettings', JSON.stringify(updatedSettings));\n            addToLog('added custom category \"'.concat(category, '\"'));\n            return category;\n        }\n        return null;\n    };\n    const movePromptToFolder = async (promptId, targetFolderId)=>{\n        if (!user) return;\n        try {\n            var _prompt_folderInfo, _folders_find;\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const currentFolderId = ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || null;\n            // Don't do anything if dropping in the same folder\n            if (currentFolderId === targetFolderId) return;\n            // Update database relationship\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(promptId, currentFolderId, targetFolderId);\n            // Update local state\n            setPrompts((prev)=>prev.map((p)=>{\n                    if (p.id === promptId) {\n                        const updatedPrompt = {\n                            ...p\n                        };\n                        if (targetFolderId) {\n                            const targetFolder = folders.find((f)=>f.id === targetFolderId);\n                            if (targetFolder) {\n                                updatedPrompt.folderInfo = {\n                                    id: targetFolder.id,\n                                    name: targetFolder.name,\n                                    color: targetFolder.color\n                                };\n                            }\n                        } else {\n                            delete updatedPrompt.folderInfo;\n                        }\n                        return updatedPrompt;\n                    }\n                    return p;\n                }));\n            const targetFolderName = targetFolderId ? ((_folders_find = folders.find((f)=>f.id === targetFolderId)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"folder\" : \"no folder\";\n            addToLog('moved \"'.concat(prompt.title, '\" to ').concat(targetFolderName));\n        } catch (err) {\n            addToLog(\"failed to move prompt: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsAddingCustomCategory(false);\n            setCustomCategoryInput(\"\");\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity\",\n                                            onClick: ()=>{\n                                                setSelectedFolder(null);\n                                                setSearchTerm(\"\");\n                                                setSelectedCategory(\"all\");\n                                                setSortBy(\"smart\");\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-black text-2xl terminal-glow select-none\",\n                                                    children: [\n                                                        \">\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"_\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                                    children: \"PROMPT MANAGER\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === 'all' ? 'bg-green-800/30 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder('all');\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, null);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === folder.id ? 'bg-green-800/30 border-green-400 border' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                onDragOver: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.dataTransfer.dropEffect = 'move';\n                                                                    setDragOverFolder(folder.id);\n                                                                },\n                                                                onDragLeave: ()=>setDragOverFolder(null),\n                                                                onDrop: (e)=>{\n                                                                    e.preventDefault();\n                                                                    if (draggedPrompt) {\n                                                                        movePromptToFolder(draggedPrompt.id, folder.id);\n                                                                    }\n                                                                    setDragOverFolder(null);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: (open)=>{\n                                                                setIsNewPromptDialogOpen(open);\n                                                                if (!open) {\n                                                                    setIsAddingCustomCategory(false);\n                                                                    setCustomCategoryInput(\"\");\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 800,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 803,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 809,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 816,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        !isAddingCustomCategory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                                value: newPrompt.category,\n                                                                                                onValueChange: (value)=>{\n                                                                                                    if (value === \"add_custom\") {\n                                                                                                        setIsAddingCustomCategory(true);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    } else {\n                                                                                                        setNewPrompt((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                category: value\n                                                                                                            }));\n                                                                                                    }\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                            placeholder: \"Select category\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 831,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 830,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                        children: [\n                                                                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                    value: category,\n                                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                                }, category, false, {\n                                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                    lineNumber: 835,\n                                                                                                                    columnNumber: 39\n                                                                                                                }, this)),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: \"add_custom\",\n                                                                                                                className: \"text-blue-400 hover:bg-gray-700 font-semibold\",\n                                                                                                                children: \"+ Add Custom Category\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 839,\n                                                                                                                columnNumber: 37\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 833,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 819,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 818,\n                                                                                            columnNumber: 31\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                    placeholder: \"Enter custom category...\",\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                    value: customCategoryInput,\n                                                                                                    onChange: (e)=>setCustomCategoryInput(e.target.value),\n                                                                                                    onKeyPress: (e)=>{\n                                                                                                        if (e.key === 'Enter' && customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    autoFocus: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 847,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    onClick: ()=>{\n                                                                                                        if (customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                    children: \"Add\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 864,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    variant: \"outline\",\n                                                                                                    onClick: ()=>{\n                                                                                                        setIsAddingCustomCategory(false);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    },\n                                                                                                    className: \"border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 880,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 846,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 815,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 895,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 901,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 900,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 904,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 906,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 903,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 896,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 894,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 917,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 928,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 935,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 938,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 944,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 959,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 958,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 963,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 976,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 982,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 981,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 986,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 984,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 977,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 975,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 994,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1000,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 999,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1003,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 1005,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1002,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 995,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 993,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1016,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 962,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 956,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1030,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1035,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1036,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1034,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1045,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1051,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1050,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1055,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1053,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1046,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1044,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1064,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1063,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Custom Categories\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1077,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex space-x-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                        placeholder: \"Add custom category...\",\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        value: newCustomCategory,\n                                                                                                        onChange: (e)=>setNewCustomCategory(e.target.value),\n                                                                                                        onKeyPress: (e)=>{\n                                                                                                            if (e.key === 'Enter' && newCustomCategory.trim()) {\n                                                                                                                const category = newCustomCategory.trim().toLowerCase();\n                                                                                                                if (!allCategories.includes(category)) {\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: [\n                                                                                                                                ...prev.customCategories,\n                                                                                                                                category\n                                                                                                                            ]\n                                                                                                                        }));\n                                                                                                                    setNewCustomCategory(\"\");\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1080,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                        size: \"sm\",\n                                                                                                        onClick: ()=>{\n                                                                                                            const category = newCustomCategory.trim().toLowerCase();\n                                                                                                            if (category && !allCategories.includes(category)) {\n                                                                                                                setUserSettings((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        customCategories: [\n                                                                                                                            ...prev.customCategories,\n                                                                                                                            category\n                                                                                                                        ]\n                                                                                                                    }));\n                                                                                                                setNewCustomCategory(\"\");\n                                                                                                            }\n                                                                                                        },\n                                                                                                        className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                        children: \"Add\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1098,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1079,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            userSettings.customCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"space-y-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-green-500\",\n                                                                                                        children: \"Your custom categories:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1118,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                                        children: userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                                                                variant: \"outline\",\n                                                                                                                className: \"text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: prev.customCategories.filter((c)=>c !== category)\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: [\n                                                                                                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                                                                                                    \" \\xd7\"\n                                                                                                                ]\n                                                                                                            }, category, true, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 1121,\n                                                                                                                columnNumber: 39\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1119,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: \"Click to remove\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1136,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1117,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1078,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1076,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1143,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1145,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1146,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1147,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1144,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1142,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1152,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1163,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1151,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1033,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"general\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: category,\n                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                }, category, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1239,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: sortBy,\n                                                onValueChange: setSortBy,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-40 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"smart\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Smart Sort\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"rating\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1260,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"date\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1261,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"title\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1225,\n                                        columnNumber: 15\n                                    }, this),\n                                    folders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-semibold\",\n                                                    children: \"Quick Access Folders:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1271,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1270,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 \".concat(dragOverFolder === 'all' ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50', \" \").concat(selectedFolder === null ? 'bg-green-900/30 border-green-500' : ''),\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        onDragOver: (e)=>{\n                                                            e.preventDefault();\n                                                            e.dataTransfer.dropEffect = 'move';\n                                                            setDragOverFolder('all');\n                                                        },\n                                                        onDragLeave: ()=>setDragOverFolder(null),\n                                                        onDrop: (e)=>{\n                                                            e.preventDefault();\n                                                            if (draggedPrompt) {\n                                                                movePromptToFolder(draggedPrompt.id, null);\n                                                            }\n                                                            setDragOverFolder(null);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1295,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-300 text-center font-medium\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-500\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    prompts.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1298,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 \".concat(dragOverFolder === folder.id ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50', \" \").concat(selectedFolder === folder.id ? 'bg-green-900/30 border-green-500' : ''),\n                                                            onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder(folder.id);\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, folder.id);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 \".concat(folder.color, \" rounded-lg flex items-center justify-center mb-2\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1324,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1323,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-300 text-center font-medium max-w-16 truncate\",\n                                                                    children: folder.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1326,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        folder.promptCount || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1329,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, folder.id, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1303,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1273,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1340,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1339,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1352,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1344,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                draggable: true,\n                                                onDragStart: (e)=>{\n                                                    setDraggedPrompt(prompt);\n                                                    e.dataTransfer.effectAllowed = 'move';\n                                                    e.dataTransfer.setData('text/plain', prompt.id);\n                                                    // Add visual feedback\n                                                    e.currentTarget.style.opacity = '0.5';\n                                                },\n                                                onDragEnd: (e)=>{\n                                                    setDraggedPrompt(null);\n                                                    setDragOverFolder(null);\n                                                    e.currentTarget.style.opacity = '1';\n                                                },\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 opacity-30 hover:opacity-60 transition-opacity\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-green-400 text-xs\",\n                                                                            children: \"⋮⋮\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1384,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1383,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1386,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1388,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1391,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1389,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1387,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1416,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1421,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1411,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1428,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1430,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1431,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1429,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1440,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1434,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1448,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1442,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1456,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1450,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1433,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1427,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1362,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 1184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: draggedPrompt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: [\n                                                    'Dragging \"',\n                                                    draggedPrompt.title,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1474,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: \"Drop on folder to move\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1475,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1479,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1480,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    filteredPrompts.length,\n                                                    \" prompts\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1482,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    folders.length,\n                                                    \" folders\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1470,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1488,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1489,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1487,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1469,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1468,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 658,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 656,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 655,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"w/yogWVc7kQIbA6V1SOhU3DfAcQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});