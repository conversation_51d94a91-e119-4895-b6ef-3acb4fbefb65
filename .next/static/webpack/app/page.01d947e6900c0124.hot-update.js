"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   folderService: () => (/* binding */ folderService),\n/* harmony export */   promptFolderService: () => (/* binding */ promptFolderService),\n/* harmony export */   promptService: () => (/* binding */ promptService),\n/* harmony export */   subscriptions: () => (/* binding */ subscriptions)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Prompt operations\nconst promptService = {\n    // Get all prompts for the current user\n    async getAll (userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Get a single prompt by ID\n    async getById (id, userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').select('*').eq('id', id).eq('user_id', userId).single();\n        if (error) throw error;\n        return data;\n    },\n    // Create a new prompt\n    async create (prompt) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').insert(prompt).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update a prompt\n    async update (id, updates, userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id).eq('user_id', userId).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete a prompt\n    async delete (id, userId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').delete().eq('id', id).eq('user_id', userId);\n        if (error) throw error;\n    },\n    // Update last used timestamp\n    async updateLastUsed (id, userId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').update({\n            last_used: new Date().toISOString()\n        }).eq('id', id).eq('user_id', userId);\n        if (error) throw error;\n    },\n    // Toggle pin status\n    async togglePin (id, userId) {\n        // First get the current pin status\n        const { data: prompt, error: fetchError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').select('is_pinned').eq('id', id).eq('user_id', userId).single();\n        if (fetchError) throw fetchError;\n        // Toggle the pin status\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').update({\n            is_pinned: !prompt.is_pinned\n        }).eq('id', id).eq('user_id', userId).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Search prompts\n    async search (query, userId, category) {\n        let queryBuilder = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompts').select('*').eq('user_id', userId);\n        if (category && category !== 'all') {\n            queryBuilder = queryBuilder.eq('category', category);\n        }\n        // Use text search for title and content\n        queryBuilder = queryBuilder.or(\"title.ilike.%\".concat(query, \"%,content.ilike.%\").concat(query, \"%\"));\n        const { data, error } = await queryBuilder.order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n// Folder operations\nconst folderService = {\n    // Get all folders for the current user\n    async getAll (userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('folders').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    // Create a new folder\n    async create (folder) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('folders').insert(folder).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Update a folder\n    async update (id, updates, userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('folders').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id).eq('user_id', userId).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Delete a folder\n    async delete (id, userId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('folders').delete().eq('id', id).eq('user_id', userId);\n        if (error) throw error;\n    },\n    // Get folder with prompt count\n    async getWithPromptCount (userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('folders').select(\"\\n        *,\\n        prompt_folders(count)\\n      \").eq('user_id', userId);\n        if (error) throw error;\n        return data;\n    }\n};\n// Prompt-Folder relationship operations\nconst promptFolderService = {\n    // Add prompt to folder\n    async addPromptToFolder (promptId, folderId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompt_folders').insert({\n            prompt_id: promptId,\n            folder_id: folderId\n        }).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Remove prompt from folder\n    async removePromptFromFolder (promptId, folderId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompt_folders').delete().eq('prompt_id', promptId).eq('folder_id', folderId);\n        if (error) throw error;\n    },\n    // Get all prompts in a folder\n    async getPromptsInFolder (folderId, userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompt_folders').select(\"\\n        prompt_id,\\n        prompts!inner(*)\\n      \").eq('folder_id', folderId).eq('prompts.user_id', userId);\n        if (error) throw error;\n        return (data === null || data === void 0 ? void 0 : data.map((item)=>item.prompts)) || [];\n    },\n    // Get folder for a prompt\n    async getFolderForPrompt (promptId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('prompt_folders').select(\"\\n        folder_id,\\n        folders(*)\\n      \").eq('prompt_id', promptId).single();\n        if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned\n        ;\n        return (data === null || data === void 0 ? void 0 : data.folders) || null;\n    },\n    // Update prompt folder (move to different folder)\n    async updatePromptFolder (promptId, oldFolderId, newFolderId) {\n        // Remove from old folder if exists\n        if (oldFolderId) {\n            await this.removePromptFromFolder(promptId, oldFolderId);\n        }\n        // Add to new folder if specified\n        if (newFolderId) {\n            await this.addPromptToFolder(promptId, newFolderId);\n        }\n    }\n};\n// Real-time subscriptions\nconst subscriptions = {\n    // Subscribe to prompt changes\n    subscribeToPrompts (userId, callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.channel('prompts').on('postgres_changes', {\n            event: '*',\n            schema: 'public',\n            table: 'prompts',\n            filter: \"user_id=eq.\".concat(userId)\n        }, callback).subscribe();\n    },\n    // Subscribe to folder changes\n    subscribeToFolders (userId, callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.channel('folders').on('postgres_changes', {\n            event: '*',\n            schema: 'public',\n            table: 'folders',\n            filter: \"user_id=eq.\".concat(userId)\n        }, callback).subscribe();\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/database.ts\n"));

/***/ })

});