"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            setPrompts(data);\n            addToLog(\"loaded \".concat(data.length, \" prompts\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // For now, we'll just use category filtering since we don't have folder relationships implemented yet\n        // TODO: Add folder filtering when prompt_folders relationship is implemented\n        return matchesSearch && matchesCategory;\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add folder information for display (temporary solution)\n            if (newPrompt.folderId !== \"none\") {\n                const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                if (selectedFolder) {\n                    // Store folder info in the prompt object for display\n                    prompt.folderInfo = {\n                        id: selectedFolder.id,\n                        name: selectedFolder.name,\n                        color: selectedFolder.color\n                    };\n                }\n            }\n            // TODO: Implement proper prompt_folders table relationship in database\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: \"general\",\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 420,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 423,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 436,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.category,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        category: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select category\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 442,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 441,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"general\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"General\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 445,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"development\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Development\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 446,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"communication\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Communication\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 447,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"analysis\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Analysis\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 448,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"creative\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Creative\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 449,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 444,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 437,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 454,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 460,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 459,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 463,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 465,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 462,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 455,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 453,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 497,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 503,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600\")\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 572,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-green-400 border-green-500 bg-green-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-green-300 border-green-600 bg-green-800/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-700 bg-gray-800/50\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 331,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 329,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 328,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"EXwKj4PbUwdEDzZRN+ik0q8A9vs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});