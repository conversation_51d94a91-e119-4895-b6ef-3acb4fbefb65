"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"smart\") // smart, date, title, rating\n    ;\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true,\n        customCategories: []\n    });\n    const [newCustomCategory, setNewCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }).sort((a, b)=>{\n        // Sort by pinned status first (pinned items at top), then by creation date\n        if (a.is_pinned && !b.is_pinned) return -1;\n        if (!a.is_pinned && b.is_pinned) return 1;\n        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 641,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 644,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 657,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.category,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        category: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select category\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 663,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 662,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"general\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"General\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 666,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"development\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Development\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 667,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"communication\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Communication\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 668,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"analysis\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Analysis\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 669,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"creative\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Creative\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 670,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 665,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 658,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 675,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 681,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 680,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 684,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 686,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 683,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 676,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 674,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 715,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 718,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 724,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 717,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 743,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 749,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 756,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 762,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 761,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 765,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 766,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 767,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 768,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 769,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 764,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 757,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 755,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 774,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 780,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 779,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 783,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 785,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 782,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 775,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 773,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 742,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 811,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 815,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 816,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 814,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 825,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 831,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 830,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 834,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 835,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 836,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 837,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 838,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 833,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 826,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 824,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 844,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 851,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 857,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 859,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 860,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 861,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 858,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 866,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 877,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 865,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 940,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 967,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 999,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1002,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1000,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1023,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1027,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1022,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1039,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1041,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1042,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1040,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1051,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1059,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1067,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 989,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 898,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1082,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1081,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1089,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1088,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1079,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 547,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 545,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 544,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"boYLd5PeO/n203UpO7csaaWADI4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});