"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            setPrompts(data);\n            addToLog(\"loaded \".concat(data.length, \" prompts\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: newPrompt.tags,\n                user_id: user.id\n            });\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: \"general\",\n                tags: []\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: folder.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: folder.promptCount || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, folder.id, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 329,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 332,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 338,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                    value: newPrompt.category,\n                                                                                    onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                category: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                            className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                placeholder: \"Select category\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 349,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 348,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                            className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"general\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"General\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 352,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"development\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Development\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 353,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"communication\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Communication\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 354,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"analysis\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Analysis\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 355,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                    value: \"creative\",\n                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Creative\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 356,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 351,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 359,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"+ New Folder\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600\")\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 431,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-green-500 border-green-600\",\n                                                                        children: tag\n                                                                    }, tag, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"z/eT3UpYHhyWeDouPs0Gmy+DZZg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQTtBQUMwQztBQUNyQztBQUM0QjtBQUM3QjtBQUNGO0FBQ007QUFDNEI7QUFDbEM7QUFDMkQ7QUFDRjtBQTBCdkYsU0FBU2lDOztJQUN0QixNQUFNLEVBQUVDLElBQUksRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQUUsR0FBR3pCLDhEQUFPQTtJQUMxQyxNQUFNMEIsU0FBU25DLDBEQUFTQTtJQUV4QixNQUFNLENBQUNvQyxhQUFhQyxlQUFlLEdBQUd2QywrQ0FBUUEsQ0FBQyxJQUFJd0M7SUFDbkQsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMyQyxZQUFZQyxjQUFjLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2QyxrQkFBa0JDLG9CQUFvQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDK0MsU0FBU0MsV0FBVyxHQUFHaEQsK0NBQVFBLENBQVcsRUFBRTtJQUNuRCxNQUFNLENBQUNpRCxTQUFTQyxXQUFXLEdBQUdsRCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQ3ZELE1BQU0sQ0FBQ21ELGdCQUFnQkMsa0JBQWtCLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNxRCxnQkFBZ0JDLGtCQUFrQixHQUFHdEQsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDdUQsT0FBT0MsU0FBUyxHQUFHeEQsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3lELFdBQVdDLGFBQWEsR0FBRzFELCtDQUFRQSxDQUFDO1FBQ3pDMkQsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsTUFBTSxFQUFFO1FBQ1JDLFVBQVU7SUFDWjtJQUNBLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR2pFLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ2tFLHVCQUF1QkMseUJBQXlCLEdBQUduRSwrQ0FBUUEsQ0FBQztJQUVuRSxNQUFNLENBQUNvRSxhQUFhQyxlQUFlLEdBQUdyRSwrQ0FBUUEsQ0FBQztRQUM3QztLQUNEO0lBRUQsNkJBQTZCO0lBQzdCLE1BQU1zRSxjQUFjO1FBQ2xCLElBQUksQ0FBQ3BDLE1BQU07UUFFWCxJQUFJO1lBQ0ZrQixrQkFBa0I7WUFDbEIsTUFBTW1CLE9BQU8sTUFBTTNELHdEQUFhQSxDQUFDNEQsTUFBTSxDQUFDdEMsS0FBS3VDLEVBQUU7WUFDL0N6QixXQUFXdUI7WUFDWEcsU0FBUyxVQUFzQixPQUFaSCxLQUFLSSxNQUFNLEVBQUM7UUFDakMsRUFBRSxPQUFPQyxLQUFVO1lBQ2pCcEIsU0FBU29CLElBQUlDLE9BQU87WUFDcEJILFNBQVMsMEJBQXNDLE9BQVpFLElBQUlDLE9BQU87UUFDaEQsU0FBVTtZQUNSekIsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTTBCLGNBQWM7UUFDbEIsSUFBSSxDQUFDNUMsTUFBTTtRQUVYLElBQUk7WUFDRm9CLGtCQUFrQjtZQUNsQixNQUFNaUIsT0FBTyxNQUFNMUQsd0RBQWFBLENBQUMyRCxNQUFNLENBQUN0QyxLQUFLdUMsRUFBRTtZQUMvQ3ZCLFdBQVdxQjtZQUNYRyxTQUFTLFVBQXNCLE9BQVpILEtBQUtJLE1BQU0sRUFBQztRQUNqQyxFQUFFLE9BQU9DLEtBQVU7WUFDakJwQixTQUFTb0IsSUFBSUMsT0FBTztZQUNwQkgsU0FBUywwQkFBc0MsT0FBWkUsSUFBSUMsT0FBTztRQUNoRCxTQUFVO1lBQ1J2QixrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QnJELGdEQUFTQTsyQ0FBQztZQUNSLElBQUksQ0FBQ2tDLFdBQVcsQ0FBQ0QsTUFBTTtnQkFDckJHLE9BQU8wQyxJQUFJLENBQUM7WUFDZDtRQUNGOzBDQUFHO1FBQUM3QztRQUFNQztRQUFTRTtLQUFPO0lBRTFCLHVDQUF1QztJQUN2Q3BDLGdEQUFTQTsyQ0FBQztZQUNSLElBQUlpQyxNQUFNO2dCQUNSb0M7Z0JBQ0FRO2dCQUNBSixTQUFTO1lBQ1g7UUFDRjswQ0FBRztRQUFDeEM7S0FBSztJQUVULGlDQUFpQztJQUNqQ2pDLGdEQUFTQTsyQ0FBQztZQUNSLElBQUksQ0FBQ2lDLE1BQU07WUFFWCw4QkFBOEI7WUFDOUIsTUFBTThDLHFCQUFxQmxFLHdEQUFhQSxDQUFDbUUsa0JBQWtCLENBQUMvQyxLQUFLdUMsRUFBRTtzRUFBRSxDQUFDUztvQkFDcEVSLFNBQVMscUJBQXVDLE9BQWxCUSxRQUFRQyxTQUFTLEVBQUM7b0JBRWhELElBQUlELFFBQVFDLFNBQVMsS0FBSyxVQUFVO3dCQUNsQ25DO2tGQUFXb0MsQ0FBQUEsT0FBUTtvQ0FBQ0YsUUFBUUcsR0FBRzt1Q0FBS0Q7aUNBQUs7O29CQUMzQyxPQUFPLElBQUlGLFFBQVFDLFNBQVMsS0FBSyxVQUFVO3dCQUN6Q25DO2tGQUFXb0MsQ0FBQUEsT0FBUUEsS0FBS0UsR0FBRzswRkFBQ0MsQ0FBQUEsSUFBS0EsRUFBRWQsRUFBRSxLQUFLUyxRQUFRRyxHQUFHLENBQUNaLEVBQUUsR0FBR1MsUUFBUUcsR0FBRyxHQUFHRTs7O29CQUMzRSxPQUFPLElBQUlMLFFBQVFDLFNBQVMsS0FBSyxVQUFVO3dCQUN6Q25DO2tGQUFXb0MsQ0FBQUEsT0FBUUEsS0FBS0ksTUFBTTswRkFBQ0QsQ0FBQUEsSUFBS0EsRUFBRWQsRUFBRSxLQUFLUyxRQUFRTyxHQUFHLENBQUNoQixFQUFFOzs7b0JBQzdEO2dCQUNGOztZQUVBLDhCQUE4QjtZQUM5QixNQUFNaUIscUJBQXFCNUUsd0RBQWFBLENBQUM2RSxrQkFBa0IsQ0FBQ3pELEtBQUt1QyxFQUFFO3NFQUFFLENBQUNTO29CQUNwRVIsU0FBUyxxQkFBdUMsT0FBbEJRLFFBQVFDLFNBQVMsRUFBQztvQkFFaEQsSUFBSUQsUUFBUUMsU0FBUyxLQUFLLFVBQVU7d0JBQ2xDakM7a0ZBQVdrQyxDQUFBQSxPQUFRO29DQUFDRixRQUFRRyxHQUFHO3VDQUFLRDtpQ0FBSzs7b0JBQzNDLE9BQU8sSUFBSUYsUUFBUUMsU0FBUyxLQUFLLFVBQVU7d0JBQ3pDakM7a0ZBQVdrQyxDQUFBQSxPQUFRQSxLQUFLRSxHQUFHOzBGQUFDTSxDQUFBQSxJQUFLQSxFQUFFbkIsRUFBRSxLQUFLUyxRQUFRRyxHQUFHLENBQUNaLEVBQUUsR0FBR1MsUUFBUUcsR0FBRyxHQUFHTzs7O29CQUMzRSxPQUFPLElBQUlWLFFBQVFDLFNBQVMsS0FBSyxVQUFVO3dCQUN6Q2pDO2tGQUFXa0MsQ0FBQUEsT0FBUUEsS0FBS0ksTUFBTTswRkFBQ0ksQ0FBQUEsSUFBS0EsRUFBRW5CLEVBQUUsS0FBS1MsUUFBUU8sR0FBRyxDQUFDaEIsRUFBRTs7O29CQUM3RDtnQkFDRjs7WUFFQSx3QkFBd0I7WUFDeEI7bURBQU87b0JBQ0xPLG1CQUFtQmEsV0FBVztvQkFDOUJILG1CQUFtQkcsV0FBVztnQkFDaEM7O1FBQ0Y7MENBQUc7UUFBQzNEO0tBQUs7SUFFVCwyQkFBMkI7SUFDM0JqQyxnREFBU0E7MkNBQUM7WUFDUixNQUFNNkYsUUFBUUM7eURBQVksSUFBTXhELGVBQWUsSUFBSUM7d0RBQVM7WUFDNUQ7bURBQU8sSUFBTXdELGNBQWNGOztRQUM3QjswQ0FBRyxFQUFFO0lBRUwsb0RBQW9EO0lBQ3BELElBQUkzRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUM4RDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUE4Qjs7Ozs7O2tDQUM3Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXZCO0lBRUEseUNBQXlDO0lBQ3pDLElBQUksQ0FBQ2hFLE1BQU07UUFDVCxPQUFPO0lBQ1Q7SUFFQSxNQUFNaUUsa0JBQWtCcEQsUUFBUXlDLE1BQU0sQ0FBQyxDQUFDWTtRQUN0QyxNQUFNQyxnQkFDSkQsT0FBT3pDLEtBQUssQ0FBQzJDLFdBQVcsR0FBR0MsUUFBUSxDQUFDNUQsV0FBVzJELFdBQVcsT0FDMURGLE9BQU94QyxPQUFPLENBQUMwQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzVELFdBQVcyRCxXQUFXLE9BQzVERixPQUFPdEMsSUFBSSxDQUFDMEMsSUFBSSxDQUFDLENBQUNDLE1BQVFBLElBQUlILFdBQVcsR0FBR0MsUUFBUSxDQUFDNUQsV0FBVzJELFdBQVc7UUFDN0UsTUFBTUksa0JBQWtCN0QscUJBQXFCLFNBQVN1RCxPQUFPdkMsUUFBUSxLQUFLaEI7UUFDMUUsT0FBT3dELGlCQUFpQks7SUFDMUI7SUFFQSxNQUFNaEMsV0FBVyxDQUFDRztRQUNoQlIsZUFBZSxDQUFDZSxPQUFTO21CQUFJQSxLQUFLdUIsS0FBSyxDQUFDLENBQUM7Z0JBQUssdUJBQThCLE9BQVI5QjthQUFVO0lBQ2hGO0lBRUEsTUFBTStCLGFBQWEsT0FBT1I7UUFDeEIsSUFBSTtZQUNGLE1BQU1TLFVBQVVDLFNBQVMsQ0FBQ0MsU0FBUyxDQUFDWCxPQUFPeEMsT0FBTztZQUNsRCw2QkFBNkI7WUFDN0IsSUFBSTFCLE1BQU07Z0JBQ1IsTUFBTXRCLHdEQUFhQSxDQUFDb0csY0FBYyxDQUFDWixPQUFPM0IsRUFBRSxFQUFFdkMsS0FBS3VDLEVBQUU7Z0JBQ3JESCxjQUFjLG1CQUFtQjs7WUFDbkM7WUFDQUksU0FBUyxXQUF3QixPQUFiMEIsT0FBT3pDLEtBQUssRUFBQztRQUNuQyxFQUFFLE9BQU9pQixLQUFLO1lBQ1pGLFNBQVMsbUJBQWdDLE9BQWIwQixPQUFPekMsS0FBSyxFQUFDO1FBQzNDO0lBQ0Y7SUFFQSxNQUFNc0QsWUFBWSxPQUFPeEM7UUFDdkIsSUFBSSxDQUFDdkMsTUFBTTtRQUVYLElBQUk7WUFDRixNQUFNZ0YsZ0JBQWdCLE1BQU10Ryx3REFBYUEsQ0FBQ3FHLFNBQVMsQ0FBQ3hDLElBQUl2QyxLQUFLdUMsRUFBRTtZQUMvRHpCLFdBQVcsQ0FBQ29DLE9BQVNBLEtBQUtFLEdBQUcsQ0FBQyxDQUFDQyxJQUFPQSxFQUFFZCxFQUFFLEtBQUtBLEtBQUt5QyxnQkFBZ0IzQjtZQUNwRWIsU0FBUyxHQUF1RHdDLE9BQXBEQSxjQUFjQyxTQUFTLEdBQUcsV0FBVyxZQUFXLE1BQXdCLE9BQXBCRCxjQUFjdkQsS0FBSyxFQUFDO1FBQ3RGLEVBQUUsT0FBT2lCLEtBQVU7WUFDakJGLFNBQVMseUJBQXFDLE9BQVpFLElBQUlDLE9BQU87UUFDL0M7SUFDRjtJQUVBLE1BQU11QyxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU1oRjtZQUNOc0MsU0FBUztRQUNYLEVBQUUsT0FBT25CLE9BQU87WUFDZDhELFFBQVE5RCxLQUFLLENBQUMsc0JBQXNCQTtRQUN0QztJQUNGO0lBRUEsTUFBTStELGVBQWU7UUFDbkIsSUFBSSxDQUFDcEYsUUFBUSxDQUFDdUIsVUFBVUUsS0FBSyxDQUFDNEQsSUFBSSxNQUFNLENBQUM5RCxVQUFVRyxPQUFPLENBQUMyRCxJQUFJLElBQUk7UUFFbkUsSUFBSTtZQUNGdEQsb0JBQW9CO1lBQ3BCLE1BQU1tQyxTQUFTLE1BQU14Rix3REFBYUEsQ0FBQzRHLE1BQU0sQ0FBQztnQkFDeEM3RCxPQUFPRixVQUFVRSxLQUFLLENBQUM0RCxJQUFJO2dCQUMzQjNELFNBQVNILFVBQVVHLE9BQU8sQ0FBQzJELElBQUk7Z0JBQy9CMUQsVUFBVUosVUFBVUksUUFBUTtnQkFDNUJDLE1BQU1MLFVBQVVLLElBQUk7Z0JBQ3BCMkQsU0FBU3ZGLEtBQUt1QyxFQUFFO1lBQ2xCO1lBRUF6QixXQUFXb0MsQ0FBQUEsT0FBUTtvQkFBQ2dCO3VCQUFXaEI7aUJBQUs7WUFDcEMxQixhQUFhO2dCQUFFQyxPQUFPO2dCQUFJQyxTQUFTO2dCQUFJQyxVQUFVO2dCQUFXQyxNQUFNLEVBQUU7WUFBQztZQUNyRUsseUJBQXlCLE9BQU8sbUJBQW1COztZQUNuRE8sU0FBUyxtQkFBZ0MsT0FBYjBCLE9BQU96QyxLQUFLLEVBQUM7UUFDM0MsRUFBRSxPQUFPaUIsS0FBVTtZQUNqQkYsU0FBUyw0QkFBd0MsT0FBWkUsSUFBSUMsT0FBTztRQUNsRCxTQUFVO1lBQ1JaLG9CQUFvQjtRQUN0QjtJQUNGO0lBRUEscUJBQ0UsOERBQUNnQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUViLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQWlEOzs7Ozs7c0RBQ2hFLDhEQUFDd0I7NENBQUl4QixXQUFVOzs4REFDYiw4REFBQ3lCO29EQUFPekIsV0FBVTs4REFBOEM7Ozs7Ozs4REFDaEUsOERBQUN5QjtvREFBT3pCLFdBQVU7OERBQThDOzs7Ozs7OERBQ2hFLDhEQUFDeUI7b0RBQU96QixXQUFVOzhEQUE4Qzs7Ozs7OzhEQUNoRSw4REFBQ3lCO29EQUFPekIsV0FBVTs4REFBOEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHcEUsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQTBCaEUsaUJBQUFBLDJCQUFBQSxLQUFNMEYsS0FBSzs7Ozs7O3NEQUNwRCw4REFBQ0Q7NENBQ0NFLFNBQVNUOzRDQUNUbEIsV0FBVTs7OERBRVYsOERBQUN4Riw0SEFBTUE7b0RBQUN3RixXQUFVOzs7Ozs7OERBQ2xCLDhEQUFDNEI7OERBQUs7Ozs7Ozs7Ozs7OztzREFFUiw4REFBQzdCOzRDQUFJQyxXQUFVO3NEQUEwQjVELFlBQVl5RixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU03RSw4REFBQzlCO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3pGLDRIQUFRQTs0Q0FBQ3lGLFdBQVU7Ozs7OztzREFDcEIsOERBQUM0QjtzREFBSzs7Ozs7O3NEQUNOLDhEQUFDQTs0Q0FBSzVCLFdBQVU7c0RBQWtCekQ7Ozs7OztzREFDbEMsOERBQUNxRjs0Q0FBSzVCLFdBQVU7c0RBQWdCOzs7Ozs7Ozs7Ozs7OENBRWxDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM0QjtzREFBSzs7Ozs7O3NEQUNOLDhEQUFDN0I7NENBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtyQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs7OERBQ0MsOERBQUMrQjtvREFBRzlCLFdBQVU7OERBQXNEOzs7Ozs7OERBQ3BFLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWmpELFFBQVFxQyxHQUFHLENBQUMsQ0FBQzJDLHVCQUNaLDhEQUFDaEM7NERBRUNDLFdBQVU7OzhFQUVWLDhEQUFDRDtvRUFBSUMsV0FBVyxtQkFBZ0MsT0FBYitCLE9BQU9DLEtBQUs7Ozs7Ozs4RUFDL0MsOERBQUNKO29FQUFLNUIsV0FBVTs4RUFBaUMrQixPQUFPRSxJQUFJOzs7Ozs7OEVBQzVELDhEQUFDTDtvRUFBSzVCLFdBQVU7OEVBQTBCK0IsT0FBT0csV0FBVyxJQUFJOzs7Ozs7OzJEQUwzREgsT0FBT3hELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBWXRCLDhEQUFDd0I7OzhEQUNDLDhEQUFDK0I7b0RBQUc5QixXQUFVOzhEQUFzRDs7Ozs7OzhEQUNwRSw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDM0UsMERBQU1BOzREQUFDOEcsTUFBTW5FOzREQUF1Qm9FLGNBQWNuRTs7OEVBQ2pELDhEQUFDeEMsaUVBQWFBO29FQUFDNEcsT0FBTzs4RUFDcEIsNEVBQUNaO3dFQUFPekIsV0FBVTtrRkFBd0U7Ozs7Ozs7Ozs7OzhFQUk1Riw4REFBQzFFLGlFQUFhQTtvRUFBQzBFLFdBQVU7O3NGQUN2Qiw4REFBQ3pFLGdFQUFZQTtzRkFDWCw0RUFBQ0MsK0RBQVdBO2dGQUFDd0UsV0FBVTswRkFBaUI7Ozs7Ozs7Ozs7O3NGQUUxQyw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDbEYsdURBQUtBO29GQUNKd0gsYUFBWTtvRkFDWnRDLFdBQVU7b0ZBQ1Z1QyxPQUFPaEYsVUFBVUUsS0FBSztvRkFDdEIrRSxVQUFVLENBQUNDLElBQU1qRixhQUFhMEIsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFekIsT0FBT2dGLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0RkFBQzs7Ozs7OzhGQUUxRSw4REFBQ3hILDZEQUFRQTtvRkFDUHVILGFBQVk7b0ZBQ1p0QyxXQUFVO29GQUNWdUMsT0FBT2hGLFVBQVVHLE9BQU87b0ZBQ3hCOEUsVUFBVSxDQUFDQyxJQUFNakYsYUFBYTBCLENBQUFBLE9BQVM7Z0dBQUUsR0FBR0EsSUFBSTtnR0FBRXhCLFNBQVMrRSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NEZBQUM7Ozs7Ozs4RkFFNUUsOERBQUM3RywwREFBTUE7b0ZBQ0w2RyxPQUFPaEYsVUFBVUksUUFBUTtvRkFDekJnRixlQUFlLENBQUNKLFFBQVUvRSxhQUFhMEIsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFdkIsVUFBVTRFOzRGQUFNOztzR0FFM0UsOERBQUMxRyxpRUFBYUE7NEZBQUNtRSxXQUFVO3NHQUN2Qiw0RUFBQ2xFLCtEQUFXQTtnR0FBQ3dHLGFBQVk7Ozs7Ozs7Ozs7O3NHQUUzQiw4REFBQzNHLGlFQUFhQTs0RkFBQ3FFLFdBQVU7OzhHQUN2Qiw4REFBQ3BFLDhEQUFVQTtvR0FBQzJHLE9BQU07b0dBQVV2QyxXQUFVOzhHQUFtQzs7Ozs7OzhHQUN6RSw4REFBQ3BFLDhEQUFVQTtvR0FBQzJHLE9BQU07b0dBQWN2QyxXQUFVOzhHQUFtQzs7Ozs7OzhHQUM3RSw4REFBQ3BFLDhEQUFVQTtvR0FBQzJHLE9BQU07b0dBQWdCdkMsV0FBVTs4R0FBbUM7Ozs7Ozs4R0FDL0UsOERBQUNwRSw4REFBVUE7b0dBQUMyRyxPQUFNO29HQUFXdkMsV0FBVTs4R0FBbUM7Ozs7Ozs4R0FDMUUsOERBQUNwRSw4REFBVUE7b0dBQUMyRyxPQUFNO29HQUFXdkMsV0FBVTs4R0FBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RkFHOUUsOERBQUNuRix5REFBTUE7b0ZBQ0xtRixXQUFVO29GQUNWMkIsU0FBU1A7b0ZBQ1R3QixVQUFVOUUsb0JBQW9CLENBQUNQLFVBQVVFLEtBQUssQ0FBQzRELElBQUksTUFBTSxDQUFDOUQsVUFBVUcsT0FBTyxDQUFDMkQsSUFBSTs4RkFFL0V2RCxtQkFBbUIsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBSzVDLDhEQUFDMkQ7NERBQU96QixXQUFVO3NFQUF3RTs7Ozs7O3NFQUcxRiw4REFBQ3lCOzREQUFPekIsV0FBVTtzRUFBd0U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNsRyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQy9GLDRIQUFNQTt3REFBQytGLFdBQVU7Ozs7OztrRUFDbEIsOERBQUNsRix1REFBS0E7d0RBQ0p3SCxhQUFZO3dEQUNaQyxPQUFPOUY7d0RBQ1ArRixVQUFVLENBQUNDLElBQU0vRixjQUFjK0YsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dEQUM3Q3ZDLFdBQVU7Ozs7Ozs7Ozs7OzswREFHZCw4REFBQ3RFLDBEQUFNQTtnREFBQzZHLE9BQU81RjtnREFBa0JnRyxlQUFlL0Y7O2tFQUM5Qyw4REFBQ2YsaUVBQWFBO3dEQUFDbUUsV0FBVTtrRUFDdkIsNEVBQUNsRSwrREFBV0E7Ozs7Ozs7Ozs7a0VBRWQsOERBQUNILGlFQUFhQTt3REFBQ3FFLFdBQVU7OzBFQUN2Qiw4REFBQ3BFLDhEQUFVQTtnRUFBQzJHLE9BQU07Z0VBQU12QyxXQUFVOzBFQUFtQzs7Ozs7OzBFQUNyRSw4REFBQ3BFLDhEQUFVQTtnRUFBQzJHLE9BQU07Z0VBQWN2QyxXQUFVOzBFQUFtQzs7Ozs7OzBFQUM3RSw4REFBQ3BFLDhEQUFVQTtnRUFBQzJHLE9BQU07Z0VBQWdCdkMsV0FBVTswRUFBbUM7Ozs7OzswRUFDL0UsOERBQUNwRSw4REFBVUE7Z0VBQUMyRyxPQUFNO2dFQUFXdkMsV0FBVTswRUFBbUM7Ozs7OzswRUFDMUUsOERBQUNwRSw4REFBVUE7Z0VBQUMyRyxPQUFNO2dFQUFXdkMsV0FBVTswRUFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNaEYsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaL0MsK0JBQ0MsOERBQUM4Qzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7Z0RBQW1GOzs7Ozs7bURBR2xHQyxnQkFBZ0J4QixNQUFNLEtBQUssa0JBQzdCLDhEQUFDc0I7NENBQUlDLFdBQVU7c0RBQ1p2RCxjQUFjRSxxQkFBcUIsUUFBUSxrQ0FBa0M7Ozs7O21EQUdoRnNELGdCQUFnQmIsR0FBRyxDQUFDLENBQUNjLHVCQUNyQiw4REFBQ2xGLHFEQUFJQTtnREFFSGdGLFdBQVU7O2tFQUVWLDhEQUFDOUUsMkRBQVVBO3dEQUFDOEUsV0FBVTs7MEVBQ3BCLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUM3RSwwREFBU0E7d0VBQUM2RSxXQUFVO2tGQUFvQ0UsT0FBT3pDLEtBQUs7Ozs7OztrRkFDckUsOERBQUNzQzt3RUFBSUMsV0FBVTs7NEVBQ1pFLE9BQU9lLFNBQVMsa0JBQUksOERBQUM1Ryw0SEFBR0E7Z0ZBQUMyRixXQUFVOzs7Ozs7MEZBQ3BDLDhEQUFDRDtnRkFBSUMsV0FBVTswRkFDWjt1RkFBSTZDLE1BQU07aUZBQUcsQ0FBQ3pELEdBQUcsQ0FBQyxDQUFDMEQsR0FBR0Msa0JBQ3JCLDhEQUFDN0ksNEhBQUlBO3dGQUVIOEYsV0FBVyxXQUErRSxPQUFwRStDLElBQUk3QyxPQUFPOEMsTUFBTSxHQUFHLGdDQUFnQzt1RkFEckVEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQU9mLDhEQUFDaEQ7Z0VBQUlDLFdBQVU7MEVBQ1pFLE9BQU90QyxJQUFJLENBQUN3QixHQUFHLENBQUMsQ0FBQ21CLG9CQUNoQiw4REFBQ25GLHVEQUFLQTt3RUFBVzZILFNBQVE7d0VBQVVqRCxXQUFVO2tGQUMxQ087dUVBRFNBOzs7Ozs7Ozs7Ozs7Ozs7O2tFQU1sQiw4REFBQ3RGLDREQUFXQTs7MEVBQ1YsOERBQUNvRTtnRUFBRVcsV0FBVTswRUFBNENFLE9BQU94QyxPQUFPOzs7Ozs7MEVBQ3ZFLDhEQUFDcUM7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDNEI7a0ZBQU0sSUFBSXRGLEtBQUs0RCxPQUFPZ0QsVUFBVSxFQUFFQyxrQkFBa0I7Ozs7OztvRUFDcERqRCxPQUFPa0QsU0FBUyxrQkFBSSw4REFBQ3hCOzs0RUFBSzs0RUFBTyxJQUFJdEYsS0FBSzRELE9BQU9rRCxTQUFTLEVBQUVELGtCQUFrQjs7Ozs7Ozs7Ozs7OzswRUFFakYsOERBQUNwRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNuRix5REFBTUE7d0VBQ0x3SSxNQUFLO3dFQUNMSixTQUFRO3dFQUNSdEIsU0FBUyxJQUFNakIsV0FBV1I7d0VBQzFCRixXQUFVO2tGQUVWLDRFQUFDN0YsNEhBQUlBOzRFQUFDNkYsV0FBVTs7Ozs7Ozs7Ozs7a0ZBRWxCLDhEQUFDbkYseURBQU1BO3dFQUNMd0ksTUFBSzt3RUFDTEosU0FBUTt3RUFDUnRCLFNBQVMsSUFBTVosVUFBVWIsT0FBTzNCLEVBQUU7d0VBQ2xDeUIsV0FBVTtrRkFFViw0RUFBQzNGLDRIQUFHQTs0RUFBQzJGLFdBQVU7Ozs7Ozs7Ozs7O2tGQUVqQiw4REFBQ25GLHlEQUFNQTt3RUFDTHdJLE1BQUs7d0VBQ0xKLFNBQVE7d0VBQ1JqRCxXQUFVO2tGQUVWLDRFQUFDNUYsNEhBQUlBOzRFQUFDNEYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQXREakJFLE9BQU8zQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWtFeEIsOERBQUN3Qjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM0QjtzREFBSzs7Ozs7O3NEQUNOLDhEQUFDQTtzREFBSzs7Ozs7O3NEQUNOLDhEQUFDQTs7Z0RBQU0zQixnQkFBZ0J4QixNQUFNO2dEQUFDOzs7Ozs7O3NEQUM5Qiw4REFBQ21EO3NEQUFLOzs7Ozs7c0RBQ04sOERBQUNBOztnREFBTTdFLFFBQVEwQixNQUFNO2dEQUFDOzs7Ozs7Ozs7Ozs7OzhDQUV4Qiw4REFBQ3NCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzFGLDRIQUFLQTs0Q0FBQzBGLFdBQVU7Ozs7OztzREFDakIsOERBQUM0Qjs7Z0RBQUs7Z0RBQWdCMUQsV0FBVyxDQUFDQSxZQUFZTyxNQUFNLEdBQUcsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF6RTtHQWxkd0IxQzs7UUFDYXRCLDBEQUFPQTtRQUMzQlQsc0RBQVNBOzs7S0FGRitCIiwic291cmNlcyI6WyIvVXNlcnMvdG9tbXlsZWUvRGVza3RvcC9leHBlcmltZW50L1Byb3BtdCBtYW5hZ2VyL2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxuaW1wb3J0IHsgU2VhcmNoLCBTdGFyLCBDb3B5LCBFZGl0LCBQaW4sIENsb2NrLCBUZXJtaW5hbCwgTG9nT3V0IH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvY29udGV4dHMvQXV0aENvbnRleHRcIlxuaW1wb3J0IHsgcHJvbXB0U2VydmljZSwgZm9sZGVyU2VydmljZSwgc3Vic2NyaXB0aW9ucyB9IGZyb20gXCJAL2xpYi9kYXRhYmFzZVwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RleHRhcmVhXCJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JhZGdlXCJcbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nSGVhZGVyLCBEaWFsb2dUaXRsZSwgRGlhbG9nVHJpZ2dlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiXG5cbmludGVyZmFjZSBQcm9tcHQge1xuICBpZDogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgY29udGVudDogc3RyaW5nXG4gIGNhdGVnb3J5OiBzdHJpbmdcbiAgdGFnczogc3RyaW5nW11cbiAgcmF0aW5nOiBudW1iZXJcbiAgaXNfcGlubmVkOiBib29sZWFuXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgbGFzdF91c2VkOiBzdHJpbmcgfCBudWxsXG4gIHVzZXJfaWQ6IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgRm9sZGVyVHlwZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGNvbG9yOiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xuICB1c2VyX2lkOiBzdHJpbmdcbiAgcHJvbXB0Q291bnQ/OiBudW1iZXJcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGVybWluYWxQcm9tcHRNYW5hZ2VyKCkge1xuICBjb25zdCB7IHVzZXIsIGxvYWRpbmcsIHNpZ25PdXQgfSA9IHVzZUF1dGgoKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gIGNvbnN0IFtjdXJyZW50VGltZSwgc2V0Q3VycmVudFRpbWVdID0gdXNlU3RhdGUobmV3IERhdGUoKSlcbiAgY29uc3QgW2N1cnJlbnRQYXRoLCBzZXRDdXJyZW50UGF0aF0gPSB1c2VTdGF0ZShcIi9wcm9tcHRzXCIpXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKFwiXCIpXG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlKFwiYWxsXCIpXG4gIGNvbnN0IFtwcm9tcHRzLCBzZXRQcm9tcHRzXSA9IHVzZVN0YXRlPFByb21wdFtdPihbXSlcbiAgY29uc3QgW2ZvbGRlcnMsIHNldEZvbGRlcnNdID0gdXNlU3RhdGU8Rm9sZGVyVHlwZVtdPihbXSlcbiAgY29uc3QgW2xvYWRpbmdQcm9tcHRzLCBzZXRMb2FkaW5nUHJvbXB0c10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbbG9hZGluZ0ZvbGRlcnMsIHNldExvYWRpbmdGb2xkZXJzXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW25ld1Byb21wdCwgc2V0TmV3UHJvbXB0XSA9IHVzZVN0YXRlKHtcbiAgICB0aXRsZTogXCJcIixcbiAgICBjb250ZW50OiBcIlwiLFxuICAgIGNhdGVnb3J5OiBcImdlbmVyYWxcIixcbiAgICB0YWdzOiBbXSBhcyBzdHJpbmdbXSxcbiAgICBmb2xkZXJJZDogXCJcIlxuICB9KVxuICBjb25zdCBbaXNDcmVhdGluZ1Byb21wdCwgc2V0SXNDcmVhdGluZ1Byb21wdF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzTmV3UHJvbXB0RGlhbG9nT3Blbiwgc2V0SXNOZXdQcm9tcHREaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IFthY3Rpdml0eUxvZywgc2V0QWN0aXZpdHlMb2ddID0gdXNlU3RhdGUoW1xuICAgIFwidXNlckBwcm9tcHRtYW5hZ2VyOiBpbml0aWFsaXppbmcgc3lzdGVtLi4uXCIsXG4gIF0pXG5cbiAgLy8gTG9hZCBwcm9tcHRzIGZyb20gZGF0YWJhc2VcbiAgY29uc3QgbG9hZFByb21wdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nUHJvbXB0cyh0cnVlKVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHByb21wdFNlcnZpY2UuZ2V0QWxsKHVzZXIuaWQpXG4gICAgICBzZXRQcm9tcHRzKGRhdGEpXG4gICAgICBhZGRUb0xvZyhgbG9hZGVkICR7ZGF0YS5sZW5ndGh9IHByb21wdHNgKVxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBzZXRFcnJvcihlcnIubWVzc2FnZSlcbiAgICAgIGFkZFRvTG9nKGBlcnJvciBsb2FkaW5nIHByb21wdHM6ICR7ZXJyLm1lc3NhZ2V9YClcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZ1Byb21wdHMoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgLy8gTG9hZCBmb2xkZXJzIGZyb20gZGF0YWJhc2VcbiAgY29uc3QgbG9hZEZvbGRlcnMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nRm9sZGVycyh0cnVlKVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGZvbGRlclNlcnZpY2UuZ2V0QWxsKHVzZXIuaWQpXG4gICAgICBzZXRGb2xkZXJzKGRhdGEpXG4gICAgICBhZGRUb0xvZyhgbG9hZGVkICR7ZGF0YS5sZW5ndGh9IGZvbGRlcnNgKVxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBzZXRFcnJvcihlcnIubWVzc2FnZSlcbiAgICAgIGFkZFRvTG9nKGBlcnJvciBsb2FkaW5nIGZvbGRlcnM6ICR7ZXJyLm1lc3NhZ2V9YClcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZ0ZvbGRlcnMoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgLy8gQXV0aGVudGljYXRpb24gY2hlY2tcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWxvYWRpbmcgJiYgIXVzZXIpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvYXV0aC9sb2dpbicpXG4gICAgfVxuICB9LCBbdXNlciwgbG9hZGluZywgcm91dGVyXSlcblxuICAvLyBMb2FkIGRhdGEgd2hlbiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlcikge1xuICAgICAgbG9hZFByb21wdHMoKVxuICAgICAgbG9hZEZvbGRlcnMoKVxuICAgICAgYWRkVG9Mb2coXCJzeXN0ZW0gcmVhZHlcIilcbiAgICB9XG4gIH0sIFt1c2VyXSlcblxuICAvLyBTZXQgdXAgcmVhbC10aW1lIHN1YnNjcmlwdGlvbnNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVyblxuXG4gICAgLy8gU3Vic2NyaWJlIHRvIHByb21wdCBjaGFuZ2VzXG4gICAgY29uc3QgcHJvbXB0U3Vic2NyaXB0aW9uID0gc3Vic2NyaXB0aW9ucy5zdWJzY3JpYmVUb1Byb21wdHModXNlci5pZCwgKHBheWxvYWQpID0+IHtcbiAgICAgIGFkZFRvTG9nKGByZWFsLXRpbWUgdXBkYXRlOiAke3BheWxvYWQuZXZlbnRUeXBlfSBwcm9tcHRgKVxuXG4gICAgICBpZiAocGF5bG9hZC5ldmVudFR5cGUgPT09ICdJTlNFUlQnKSB7XG4gICAgICAgIHNldFByb21wdHMocHJldiA9PiBbcGF5bG9hZC5uZXcsIC4uLnByZXZdKVxuICAgICAgfSBlbHNlIGlmIChwYXlsb2FkLmV2ZW50VHlwZSA9PT0gJ1VQREFURScpIHtcbiAgICAgICAgc2V0UHJvbXB0cyhwcmV2ID0+IHByZXYubWFwKHAgPT4gcC5pZCA9PT0gcGF5bG9hZC5uZXcuaWQgPyBwYXlsb2FkLm5ldyA6IHApKVxuICAgICAgfSBlbHNlIGlmIChwYXlsb2FkLmV2ZW50VHlwZSA9PT0gJ0RFTEVURScpIHtcbiAgICAgICAgc2V0UHJvbXB0cyhwcmV2ID0+IHByZXYuZmlsdGVyKHAgPT4gcC5pZCAhPT0gcGF5bG9hZC5vbGQuaWQpKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICAvLyBTdWJzY3JpYmUgdG8gZm9sZGVyIGNoYW5nZXNcbiAgICBjb25zdCBmb2xkZXJTdWJzY3JpcHRpb24gPSBzdWJzY3JpcHRpb25zLnN1YnNjcmliZVRvRm9sZGVycyh1c2VyLmlkLCAocGF5bG9hZCkgPT4ge1xuICAgICAgYWRkVG9Mb2coYHJlYWwtdGltZSB1cGRhdGU6ICR7cGF5bG9hZC5ldmVudFR5cGV9IGZvbGRlcmApXG5cbiAgICAgIGlmIChwYXlsb2FkLmV2ZW50VHlwZSA9PT0gJ0lOU0VSVCcpIHtcbiAgICAgICAgc2V0Rm9sZGVycyhwcmV2ID0+IFtwYXlsb2FkLm5ldywgLi4ucHJldl0pXG4gICAgICB9IGVsc2UgaWYgKHBheWxvYWQuZXZlbnRUeXBlID09PSAnVVBEQVRFJykge1xuICAgICAgICBzZXRGb2xkZXJzKHByZXYgPT4gcHJldi5tYXAoZiA9PiBmLmlkID09PSBwYXlsb2FkLm5ldy5pZCA/IHBheWxvYWQubmV3IDogZikpXG4gICAgICB9IGVsc2UgaWYgKHBheWxvYWQuZXZlbnRUeXBlID09PSAnREVMRVRFJykge1xuICAgICAgICBzZXRGb2xkZXJzKHByZXYgPT4gcHJldi5maWx0ZXIoZiA9PiBmLmlkICE9PSBwYXlsb2FkLm9sZC5pZCkpXG4gICAgICB9XG4gICAgfSlcblxuICAgIC8vIENsZWFudXAgc3Vic2NyaXB0aW9uc1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBwcm9tcHRTdWJzY3JpcHRpb24udW5zdWJzY3JpYmUoKVxuICAgICAgZm9sZGVyU3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKClcbiAgICB9XG4gIH0sIFt1c2VyXSlcblxuICAvLyBVcGRhdGUgdGltZSBldmVyeSBzZWNvbmRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0aW1lciA9IHNldEludGVydmFsKCgpID0+IHNldEN1cnJlbnRUaW1lKG5ldyBEYXRlKCkpLCAxMDAwKVxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKVxuICB9LCBbXSlcblxuICAvLyBTaG93IGxvYWRpbmcgc2NyZWVuIHdoaWxlIGNoZWNraW5nIGF1dGhlbnRpY2F0aW9uXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktOTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgcC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LWxnIG1iLTRcIj5Mb2FkaW5nLi4uPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItZ3JlZW4tNDAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBSZWRpcmVjdCB0byBsb2dpbiBpZiBub3QgYXV0aGVudGljYXRlZFxuICBpZiAoIXVzZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgY29uc3QgZmlsdGVyZWRQcm9tcHRzID0gcHJvbXB0cy5maWx0ZXIoKHByb21wdCkgPT4ge1xuICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPVxuICAgICAgcHJvbXB0LnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgcHJvbXB0LmNvbnRlbnQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICBwcm9tcHQudGFncy5zb21lKCh0YWcpID0+IHRhZy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkpXG4gICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gc2VsZWN0ZWRDYXRlZ29yeSA9PT0gXCJhbGxcIiB8fCBwcm9tcHQuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnlcbiAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzQ2F0ZWdvcnlcbiAgfSlcblxuICBjb25zdCBhZGRUb0xvZyA9IChtZXNzYWdlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRBY3Rpdml0eUxvZygocHJldikgPT4gWy4uLnByZXYuc2xpY2UoLTQpLCBgdXNlckBwcm9tcHRtYW5hZ2VyOiAke21lc3NhZ2V9YF0pXG4gIH1cblxuICBjb25zdCBjb3B5UHJvbXB0ID0gYXN5bmMgKHByb21wdDogUHJvbXB0KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHByb21wdC5jb250ZW50KVxuICAgICAgLy8gVXBkYXRlIGxhc3QgdXNlZCB0aW1lc3RhbXBcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIGF3YWl0IHByb21wdFNlcnZpY2UudXBkYXRlTGFzdFVzZWQocHJvbXB0LmlkLCB1c2VyLmlkKVxuICAgICAgICBsb2FkUHJvbXB0cygpIC8vIFJlZnJlc2ggdGhlIGxpc3RcbiAgICAgIH1cbiAgICAgIGFkZFRvTG9nKGBjb3BpZWQgXCIke3Byb21wdC50aXRsZX1cImApXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBhZGRUb0xvZyhgZmFpbGVkIHRvIGNvcHkgXCIke3Byb21wdC50aXRsZX1cImApXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdG9nZ2xlUGluID0gYXN5bmMgKGlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVwZGF0ZWRQcm9tcHQgPSBhd2FpdCBwcm9tcHRTZXJ2aWNlLnRvZ2dsZVBpbihpZCwgdXNlci5pZClcbiAgICAgIHNldFByb21wdHMoKHByZXYpID0+IHByZXYubWFwKChwKSA9PiAocC5pZCA9PT0gaWQgPyB1cGRhdGVkUHJvbXB0IDogcCkpKVxuICAgICAgYWRkVG9Mb2coYCR7dXBkYXRlZFByb21wdC5pc19waW5uZWQgPyBcInBpbm5lZFwiIDogXCJ1bnBpbm5lZFwifSBcIiR7dXBkYXRlZFByb21wdC50aXRsZX1cImApXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIGFkZFRvTG9nKGBmYWlsZWQgdG8gdG9nZ2xlIHBpbjogJHtlcnIubWVzc2FnZX1gKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNpZ25PdXQoKVxuICAgICAgYWRkVG9Mb2coXCJzaWduZWQgb3V0XCIpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNpZ25pbmcgb3V0OicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGNyZWF0ZVByb21wdCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIW5ld1Byb21wdC50aXRsZS50cmltKCkgfHwgIW5ld1Byb21wdC5jb250ZW50LnRyaW0oKSkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNDcmVhdGluZ1Byb21wdCh0cnVlKVxuICAgICAgY29uc3QgcHJvbXB0ID0gYXdhaXQgcHJvbXB0U2VydmljZS5jcmVhdGUoe1xuICAgICAgICB0aXRsZTogbmV3UHJvbXB0LnRpdGxlLnRyaW0oKSxcbiAgICAgICAgY29udGVudDogbmV3UHJvbXB0LmNvbnRlbnQudHJpbSgpLFxuICAgICAgICBjYXRlZ29yeTogbmV3UHJvbXB0LmNhdGVnb3J5LFxuICAgICAgICB0YWdzOiBuZXdQcm9tcHQudGFncyxcbiAgICAgICAgdXNlcl9pZDogdXNlci5pZFxuICAgICAgfSlcblxuICAgICAgc2V0UHJvbXB0cyhwcmV2ID0+IFtwcm9tcHQsIC4uLnByZXZdKVxuICAgICAgc2V0TmV3UHJvbXB0KHsgdGl0bGU6IFwiXCIsIGNvbnRlbnQ6IFwiXCIsIGNhdGVnb3J5OiBcImdlbmVyYWxcIiwgdGFnczogW10gfSlcbiAgICAgIHNldElzTmV3UHJvbXB0RGlhbG9nT3BlbihmYWxzZSkgLy8gQ2xvc2UgdGhlIGRpYWxvZ1xuICAgICAgYWRkVG9Mb2coYGNyZWF0ZWQgcHJvbXB0IFwiJHtwcm9tcHQudGl0bGV9XCJgKVxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBhZGRUb0xvZyhgZmFpbGVkIHRvIGNyZWF0ZSBwcm9tcHQ6ICR7ZXJyLm1lc3NhZ2V9YClcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZ1Byb21wdChmYWxzZSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktOTAwIHAtMFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1zY3JlZW5cIj5cbiAgICAgICAgey8qIFRlcm1pbmFsIEZyYW1lICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGgtc2NyZWVuIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgIHsvKiBUb3AgTmF2aWdhdGlvbiBCYXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCBweC02IHB5LTMgYm9yZGVyLWIgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgZm9udC1ib2xkIHRleHQtbGcgdGVybWluYWwtZ2xvd1wiPlBST01QVCBNQU5BR0VSPC9kaXY+XG4gICAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgYmctZ3JheS02MDAgcm91bmRlZC1mdWxsIHB4LTQgcHktMVwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBob3Zlcjp0ZXh0LWdyZWVuLTMwMCB0ZXh0LXNtXCI+SG9tZTwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBob3Zlcjp0ZXh0LWdyZWVuLTMwMCB0ZXh0LXNtXCI+Rm9sZGVyczwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBob3Zlcjp0ZXh0LWdyZWVuLTMwMCB0ZXh0LXNtXCI+U2V0dGluZ3M8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgaG92ZXI6dGV4dC1ncmVlbi0zMDAgdGV4dC1zbVwiPkhlbHA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LXNtXCI+e3VzZXI/LmVtYWlsfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNpZ25PdXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBob3Zlcjp0ZXh0LWdyZWVuLTMwMCB0ZXh0LXNtIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPExvZ091dCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPkxvZ291dDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIHRleHQtc21cIj57Y3VycmVudFRpbWUudG9Mb2NhbGVUaW1lU3RyaW5nKCl9PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUGF0aCBEaXNwbGF5ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgcHgtNiBweS0yIGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1ncmVlbi00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxUZXJtaW5hbCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj51c2VyQHByb21wdG1hbmFnZXI6PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwXCI+e2N1cnJlbnRQYXRofTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+Xzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgPHNwYW4+T25saW5lPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLVtjYWxjKDEwMHZoLTEyMHB4KV1cIj5cbiAgICAgICAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTY0IGJnLWdyYXktOTAwIGJvcmRlci1yIGJvcmRlci1ncmF5LTcwMCBwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7LyogRm9sZGVycyBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgdGV4dC1zbSBmb250LWJvbGQgbWItMiB0ZXJtaW5hbC1nbG93XCI+Jmd0OyBGT0xERVJTPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb2xkZXJzLm1hcCgoZm9sZGVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtmb2xkZXIuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcC0yIHJvdW5kZWQgaG92ZXI6YmctZ3JheS04MDAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkICR7Zm9sZGVyLmNvbG9yfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0zMDAgdGV4dC1zbSBmbGV4LTFcIj57Zm9sZGVyLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDAgdGV4dC14c1wiPntmb2xkZXIucHJvbXB0Q291bnQgfHwgMH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIHRleHQtc20gZm9udC1ib2xkIG1iLTIgdGVybWluYWwtZ2xvd1wiPiZndDsgQUNUSU9OUzwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICA8RGlhbG9nIG9wZW49e2lzTmV3UHJvbXB0RGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc05ld1Byb21wdERpYWxvZ09wZW59PlxuICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcC0yIHRleHQtZ3JlZW4tMzAwIHRleHQtc20gaG92ZXI6YmctZ3JheS04MDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICArIE5ldyBQcm9tcHRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMCBib3JkZXItZ3JheS02MDAgdGV4dC1ncmVlbi0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMFwiPkNyZWF0ZSBOZXcgUHJvbXB0PC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQcm9tcHQgdGl0bGUuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyZWVuLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld1Byb21wdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld1Byb21wdChwcmV2ID0+ICh7IC4uLnByZXYsIHRpdGxlOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUHJvbXB0IGNvbnRlbnQuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyZWVuLTMwMCBoLTMyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3UHJvbXB0LmNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdQcm9tcHQocHJldiA9PiAoeyAuLi5wcmV2LCBjb250ZW50OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3UHJvbXB0LmNhdGVnb3J5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0TmV3UHJvbXB0KHByZXYgPT4gKHsgLi4ucHJldiwgY2F0ZWdvcnk6IHZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyZWVuLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPVwiU2VsZWN0IGNhdGVnb3J5XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyLWdyYXktNjAwIHRleHQtZ3JlZW4tMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImdlbmVyYWxcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiPkdlbmVyYWw8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImRldmVsb3BtZW50XCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0zMDAgaG92ZXI6YmctZ3JheS03MDBcIj5EZXZlbG9wbWVudDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiY29tbXVuaWNhdGlvblwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwIGhvdmVyOmJnLWdyYXktNzAwXCI+Q29tbXVuaWNhdGlvbjwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYW5hbHlzaXNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiPkFuYWx5c2lzPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJjcmVhdGl2ZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwIGhvdmVyOmJnLWdyYXktNzAwXCI+Q3JlYXRpdmU8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjcmVhdGVQcm9tcHR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzQ3JlYXRpbmdQcm9tcHQgfHwgIW5ld1Byb21wdC50aXRsZS50cmltKCkgfHwgIW5ld1Byb21wdC5jb250ZW50LnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc0NyZWF0aW5nUHJvbXB0ID8gXCJDcmVhdGluZy4uLlwiIDogXCJDcmVhdGUgUHJvbXB0XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICA8L0RpYWxvZz5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZWZ0IHAtMiB0ZXh0LWdyZWVuLTMwMCB0ZXh0LXNtIGhvdmVyOmJnLWdyYXktODAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICArIE5ldyBGb2xkZXJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBwLTIgdGV4dC1ncmVlbi0zMDAgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTgwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgSW1wb3J0L0V4cG9ydFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS04MDAgcC02XCI+XG4gICAgICAgICAgICAgIHsvKiBTZWFyY2ggYW5kIEZpbHRlciBCYXIgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSByZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyZWVuLTUwMCB3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBwcm9tcHRzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIGJnLWdyYXktOTAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyZWVuLTMwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17c2VsZWN0ZWRDYXRlZ29yeX0gb25WYWx1ZUNoYW5nZT17c2V0U2VsZWN0ZWRDYXRlZ29yeX0+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlciBjbGFzc05hbWU9XCJ3LTQ4IGJnLWdyYXktOTAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyZWVuLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgLz5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyZWVuLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImFsbFwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwIGhvdmVyOmJnLWdyYXktNzAwXCI+QWxsIENhdGVnb3JpZXM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiZGV2ZWxvcG1lbnRcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiPkRldmVsb3BtZW50PC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImNvbW11bmljYXRpb25cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiPkNvbW11bmljYXRpb248L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYW5hbHlzaXNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiPkFuYWx5c2lzPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImNyZWF0aXZlXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0zMDAgaG92ZXI6YmctZ3JheS03MDBcIj5DcmVhdGl2ZTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFByb21wdHMgR3JpZCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICAgICAge2xvYWRpbmdQcm9tcHRzID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi1mdWxsIHRleHQtY2VudGVyIHRleHQtZ3JlZW4tNDAwIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItZ3JlZW4tNDAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBMb2FkaW5nIHByb21wdHMuLi5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiBmaWx0ZXJlZFByb21wdHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi1mdWxsIHRleHQtY2VudGVyIHRleHQtZ3JlZW4tNDAwIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgICAge3NlYXJjaFRlcm0gfHwgc2VsZWN0ZWRDYXRlZ29yeSAhPT0gXCJhbGxcIiA/IFwiTm8gcHJvbXB0cyBtYXRjaCB5b3VyIHNlYXJjaC5cIiA6IFwiTm8gcHJvbXB0cyB5ZXQuIENyZWF0ZSB5b3VyIGZpcnN0IHByb21wdCFcIn1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICBmaWx0ZXJlZFByb21wdHMubWFwKChwcm9tcHQpID0+IChcbiAgICAgICAgICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICAgICAgICAgIGtleT17cHJvbXB0LmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBib3JkZXItZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyZWVuLTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIHRleHQtc20gZm9udC1ib2xkXCI+e3Byb21wdC50aXRsZX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9tcHQuaXNfcGlubmVkICYmIDxQaW4gY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyZWVuLTUwMFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDUpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyAke2kgPCBwcm9tcHQucmF0aW5nID8gXCJ0ZXh0LWdyZWVuLTQwMCBmaWxsLWN1cnJlbnRcIiA6IFwidGV4dC1ncmF5LTYwMFwifWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTEgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3Byb21wdC50YWdzLm1hcCgodGFnKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBrZXk9e3RhZ30gdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JlZW4tNTAwIGJvcmRlci1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwIHRleHQteHMgbWItMyBsaW5lLWNsYW1wLTNcIj57cHJvbXB0LmNvbnRlbnR9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC1ncmVlbi01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntuZXcgRGF0ZShwcm9tcHQuY3JlYXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAge3Byb21wdC5sYXN0X3VzZWQgJiYgPHNwYW4+VXNlZDoge25ldyBEYXRlKHByb21wdC5sYXN0X3VzZWQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfTwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNvcHlQcm9tcHQocHJvbXB0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgYm9yZGVyLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29weSBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZVBpbihwcm9tcHQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBib3JkZXItZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxQaW4gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCBib3JkZXItZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RhdHVzIEJhciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHB4LTYgcHktMiBib3JkZXItdCBib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtZ3JlZW4tNTAwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8c3Bhbj5SZWFkeTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3Bhbj58PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPntmaWx0ZXJlZFByb21wdHMubGVuZ3RofSBwcm9tcHRzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPnw8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4+e2ZvbGRlcnMubGVuZ3RofSBmb2xkZXJzPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+TGFzdCBhY3Rpdml0eToge2FjdGl2aXR5TG9nW2FjdGl2aXR5TG9nLmxlbmd0aCAtIDFdfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiU2VhcmNoIiwiU3RhciIsIkNvcHkiLCJFZGl0IiwiUGluIiwiQ2xvY2siLCJUZXJtaW5hbCIsIkxvZ091dCIsInVzZUF1dGgiLCJwcm9tcHRTZXJ2aWNlIiwiZm9sZGVyU2VydmljZSIsInN1YnNjcmlwdGlvbnMiLCJCdXR0b24iLCJJbnB1dCIsIlRleHRhcmVhIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJhZGdlIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nVHJpZ2dlciIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiVGVybWluYWxQcm9tcHRNYW5hZ2VyIiwidXNlciIsImxvYWRpbmciLCJzaWduT3V0Iiwicm91dGVyIiwiY3VycmVudFRpbWUiLCJzZXRDdXJyZW50VGltZSIsIkRhdGUiLCJjdXJyZW50UGF0aCIsInNldEN1cnJlbnRQYXRoIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzZWxlY3RlZENhdGVnb3J5Iiwic2V0U2VsZWN0ZWRDYXRlZ29yeSIsInByb21wdHMiLCJzZXRQcm9tcHRzIiwiZm9sZGVycyIsInNldEZvbGRlcnMiLCJsb2FkaW5nUHJvbXB0cyIsInNldExvYWRpbmdQcm9tcHRzIiwibG9hZGluZ0ZvbGRlcnMiLCJzZXRMb2FkaW5nRm9sZGVycyIsImVycm9yIiwic2V0RXJyb3IiLCJuZXdQcm9tcHQiLCJzZXROZXdQcm9tcHQiLCJ0aXRsZSIsImNvbnRlbnQiLCJjYXRlZ29yeSIsInRhZ3MiLCJmb2xkZXJJZCIsImlzQ3JlYXRpbmdQcm9tcHQiLCJzZXRJc0NyZWF0aW5nUHJvbXB0IiwiaXNOZXdQcm9tcHREaWFsb2dPcGVuIiwic2V0SXNOZXdQcm9tcHREaWFsb2dPcGVuIiwiYWN0aXZpdHlMb2ciLCJzZXRBY3Rpdml0eUxvZyIsImxvYWRQcm9tcHRzIiwiZGF0YSIsImdldEFsbCIsImlkIiwiYWRkVG9Mb2ciLCJsZW5ndGgiLCJlcnIiLCJtZXNzYWdlIiwibG9hZEZvbGRlcnMiLCJwdXNoIiwicHJvbXB0U3Vic2NyaXB0aW9uIiwic3Vic2NyaWJlVG9Qcm9tcHRzIiwicGF5bG9hZCIsImV2ZW50VHlwZSIsInByZXYiLCJuZXciLCJtYXAiLCJwIiwiZmlsdGVyIiwib2xkIiwiZm9sZGVyU3Vic2NyaXB0aW9uIiwic3Vic2NyaWJlVG9Gb2xkZXJzIiwiZiIsInVuc3Vic2NyaWJlIiwidGltZXIiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJkaXYiLCJjbGFzc05hbWUiLCJmaWx0ZXJlZFByb21wdHMiLCJwcm9tcHQiLCJtYXRjaGVzU2VhcmNoIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNvbWUiLCJ0YWciLCJtYXRjaGVzQ2F0ZWdvcnkiLCJzbGljZSIsImNvcHlQcm9tcHQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJ1cGRhdGVMYXN0VXNlZCIsInRvZ2dsZVBpbiIsInVwZGF0ZWRQcm9tcHQiLCJpc19waW5uZWQiLCJoYW5kbGVTaWduT3V0IiwiY29uc29sZSIsImNyZWF0ZVByb21wdCIsInRyaW0iLCJjcmVhdGUiLCJ1c2VyX2lkIiwibmF2IiwiYnV0dG9uIiwiZW1haWwiLCJvbkNsaWNrIiwic3BhbiIsInRvTG9jYWxlVGltZVN0cmluZyIsImgzIiwiZm9sZGVyIiwiY29sb3IiLCJuYW1lIiwicHJvbXB0Q291bnQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiYXNDaGlsZCIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvblZhbHVlQ2hhbmdlIiwiZGlzYWJsZWQiLCJBcnJheSIsIl8iLCJpIiwicmF0aW5nIiwidmFyaWFudCIsImNyZWF0ZWRfYXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJsYXN0X3VzZWQiLCJzaXplIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});