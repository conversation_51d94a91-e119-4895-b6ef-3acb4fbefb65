"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            setPrompts(data);\n            addToLog(\"loaded \".concat(data.length, \" prompts\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    const filteredPrompts = prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    });\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add folder information for display (temporary solution)\n            if (newPrompt.folderId !== \"none\") {\n                const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                if (selectedFolder) {\n                    // Store folder info in the prompt object for display\n                    prompt.folderInfo = {\n                        id: selectedFolder.id,\n                        name: selectedFolder.name,\n                        color: selectedFolder.color\n                    };\n                }\n            }\n            // TODO: Implement proper prompt_folders table relationship in database\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: \"general\",\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = (prompt)=>{\n        var _prompt_folderInfo;\n        // Add current folder info to editing prompt\n        const promptWithFolder = {\n            ...prompt,\n            currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n        };\n        setEditingPrompt(promptWithFolder);\n        setIsEditDialogOpen(true);\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder information if changed\n            const currentFolderId = editingPrompt.currentFolderId;\n            if (currentFolderId && currentFolderId !== \"none\") {\n                const selectedFolder = folders.find((f)=>f.id === currentFolderId);\n                if (selectedFolder) {\n                    updatedPrompt.folderInfo = {\n                        id: selectedFolder.id,\n                        name: selectedFolder.name,\n                        color: selectedFolder.color\n                    };\n                }\n            } else {\n                // Remove folder info if \"none\" selected\n                delete updatedPrompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                            children: \"PROMPT MANAGER\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 472,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 471,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 475,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 481,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 488,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.category,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        category: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select category\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 494,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 493,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"general\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"General\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 497,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"development\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Development\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 498,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"communication\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Communication\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 499,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"analysis\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Analysis\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 500,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"creative\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"Creative\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 501,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 496,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 489,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 487,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 506,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 512,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 511,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 515,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 517,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 514,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 507,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 580,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 587,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 593,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 592,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"general\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"General\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 596,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"development\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Development\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 597,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"communication\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Communication\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 598,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"analysis\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Analysis\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 599,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"creative\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"Creative\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 600,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 595,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 588,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 586,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 605,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 611,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 610,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 614,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 616,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 613,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 606,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                children: \"+ New Prompt\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 737,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600\")\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 740,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 738,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 736,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 758,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 381,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"EXwKj4PbUwdEDzZRN+ik0q8A9vs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});