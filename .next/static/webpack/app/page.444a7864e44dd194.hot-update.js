"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"smart\") // smart, date, title, rating\n    ;\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true,\n        customCategories: []\n    });\n    const [newCustomCategory, setNewCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    // Get all available categories (default + custom)\n    const allCategories = [\n        \"general\",\n        \"development\",\n        \"communication\",\n        \"analysis\",\n        \"creative\",\n        ...userSettings.customCategories\n    ];\n    // Advanced sorting function\n    const sortPrompts = (prompts)=>{\n        return prompts.sort((a, b)=>{\n            // 1. Always prioritize pinned prompts\n            if (a.is_pinned && !b.is_pinned) return -1;\n            if (!a.is_pinned && b.is_pinned) return 1;\n            // 2. Apply secondary sorting\n            switch(sortBy){\n                case \"smart\":\n                    // Smart: Pin > Rating > Recent\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"rating\":\n                    // Rating: Pin > Rating > Title\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return a.title.localeCompare(b.title);\n                case \"date\":\n                    // Date: Pin > Recent > Old\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"title\":\n                    // Title: Pin > Alphabetical\n                    return a.title.localeCompare(b.title);\n                default:\n                    return 0;\n            }\n        });\n    };\n    const filteredPrompts = sortPrompts(prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }));\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity\",\n                                            onClick: ()=>{\n                                                setSelectedFolder(null);\n                                                setSearchTerm(\"\");\n                                                setSelectedCategory(\"all\");\n                                                setSortBy(\"smart\");\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-black text-2xl terminal-glow select-none\",\n                                                    children: \">_\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                                    children: \"PROMPT MANAGER\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: setIsNewPromptDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 687,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 704,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.category,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        category: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select category\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 710,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 709,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: category,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                        }, category, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 714,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 712,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 705,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 722,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 728,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 727,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 731,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 733,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 730,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 723,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 721,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 744,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 762,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 765,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 771,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 803,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 809,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 808,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 813,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 811,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 804,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 821,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 827,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 826,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 830,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 832,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 829,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 822,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 820,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 858,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 857,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 862,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 863,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 861,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 872,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 878,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 877,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 882,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 880,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 873,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 891,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 898,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 890,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Custom Categories\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex space-x-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                        placeholder: \"Add custom category...\",\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        value: newCustomCategory,\n                                                                                                        onChange: (e)=>setNewCustomCategory(e.target.value),\n                                                                                                        onKeyPress: (e)=>{\n                                                                                                            if (e.key === 'Enter' && newCustomCategory.trim()) {\n                                                                                                                const category = newCustomCategory.trim().toLowerCase();\n                                                                                                                if (!allCategories.includes(category)) {\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: [\n                                                                                                                                ...prev.customCategories,\n                                                                                                                                category\n                                                                                                                            ]\n                                                                                                                        }));\n                                                                                                                    setNewCustomCategory(\"\");\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 907,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                        size: \"sm\",\n                                                                                                        onClick: ()=>{\n                                                                                                            const category = newCustomCategory.trim().toLowerCase();\n                                                                                                            if (category && !allCategories.includes(category)) {\n                                                                                                                setUserSettings((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        customCategories: [\n                                                                                                                            ...prev.customCategories,\n                                                                                                                            category\n                                                                                                                        ]\n                                                                                                                    }));\n                                                                                                                setNewCustomCategory(\"\");\n                                                                                                            }\n                                                                                                        },\n                                                                                                        className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                        children: \"Add\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 925,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 906,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            userSettings.customCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"space-y-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-green-500\",\n                                                                                                        children: \"Your custom categories:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 945,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                                        children: userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                                                                variant: \"outline\",\n                                                                                                                className: \"text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: prev.customCategories.filter((c)=>c !== category)\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: [\n                                                                                                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                                                                                                    \" \\xd7\"\n                                                                                                                ]\n                                                                                                            }, category, true, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 948,\n                                                                                                                columnNumber: 39\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 946,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: \"Click to remove\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 963,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 944,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 905,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 970,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 972,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 973,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 974,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 971,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 979,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 990,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 978,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1002,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1020,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1024,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1054,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1067,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"general\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1069,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1071,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1072,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: category,\n                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                }, category, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1074,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: sortBy,\n                                                onValueChange: setSortBy,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-40 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"smart\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Smart Sort\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"rating\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"date\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"title\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1098,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1109,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1130,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1133,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1131,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1129,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1158,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1163,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1170,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1172,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1173,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1171,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1182,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1190,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1184,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1198,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1192,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1095,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 1011,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1214,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                filteredPrompts.length,\n                                                \" prompts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                folders.length,\n                                                \" folders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1212,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1211,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 581,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 578,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"boYLd5PeO/n203UpO7csaaWADI4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});