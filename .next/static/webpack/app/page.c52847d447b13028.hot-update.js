"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TerminalPromptManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Copy,Edit,Folder,LogOut,Pin,Search,Star,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./lib/database.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TerminalPromptManager() {\n    var _folders_find, _folders_find1;\n    _s();\n    const { user, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [currentPath, setCurrentPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/prompts\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"smart\") // smart, date, title, rating\n    ;\n    const [selectedFolder, setSelectedFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prompts, setPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingPrompts, setLoadingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPrompt, setNewPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        category: \"general\",\n        tags: [],\n        folderId: \"none\"\n    });\n    const [isCreatingPrompt, setIsCreatingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFolderName, setNewFolderName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCreatingFolder, setIsCreatingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPrompt, setEditingPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSettings, setUserSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        theme: \"dark\",\n        defaultCategory: \"general\",\n        autoSave: true,\n        customCategories: []\n    });\n    const [newCustomCategory, setNewCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddingCustomCategory, setIsAddingCustomCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customCategoryInput, setCustomCategoryInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [draggedPrompt, setDraggedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activityLog, setActivityLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"user@promptmanager: initializing system...\"\n    ]);\n    // Load prompts from database with folder relationships\n    const loadPrompts = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingPrompts(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            // Load folder relationships for each prompt\n            const promptsWithFolders = await Promise.all(data.map(async (prompt)=>{\n                try {\n                    const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n                    if (folder) {\n                        prompt.folderInfo = {\n                            id: folder.id,\n                            name: folder.name,\n                            color: folder.color\n                        };\n                    }\n                    return prompt;\n                } catch (err) {\n                    console.error(\"Failed to load folder for prompt \".concat(prompt.id, \":\"), err);\n                    return prompt;\n                }\n            }));\n            setPrompts(promptsWithFolders);\n            addToLog(\"loaded \".concat(data.length, \" prompts with folder relationships\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading prompts: \".concat(err.message));\n        } finally{\n            setLoadingPrompts(false);\n        }\n    };\n    // Load folders from database\n    const loadFolders = async ()=>{\n        if (!user) return;\n        try {\n            setLoadingFolders(true);\n            const data = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            setFolders(data);\n            addToLog(\"loaded \".concat(data.length, \" folders\"));\n        } catch (err) {\n            setError(err.message);\n            addToLog(\"error loading folders: \".concat(err.message));\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // Update folder prompt counts\n    const updateFolderCounts = ()=>{\n        setFolders((prevFolders)=>prevFolders.map((folder)=>({\n                    ...folder,\n                    promptCount: prompts.filter((prompt)=>{\n                        var _prompt_folderInfo;\n                        return ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === folder.id;\n                    }).length\n                })));\n    };\n    // Authentication check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Load data when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (user) {\n                loadPrompts();\n                loadFolders();\n                // Load saved settings from localStorage\n                const savedSettings = localStorage.getItem('promptManagerSettings');\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setUserSettings({\n                            \"TerminalPromptManager.useEffect\": (prev)=>({\n                                    ...prev,\n                                    ...parsed\n                                })\n                        }[\"TerminalPromptManager.useEffect\"]);\n                    } catch (err) {\n                        console.error('Failed to load saved settings:', err);\n                    }\n                }\n                // Initialize user settings\n                setUserSettings({\n                    \"TerminalPromptManager.useEffect\": (prev)=>{\n                        var _user_email;\n                        return {\n                            ...prev,\n                            displayName: prev.displayName || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || \"User\"\n                        };\n                    }\n                }[\"TerminalPromptManager.useEffect\"]);\n                addToLog(\"system ready\");\n            }\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update folder counts when prompts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            updateFolderCounts();\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        prompts\n    ]);\n    // Set up real-time subscriptions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            if (!user) return;\n            // Subscribe to prompt changes\n            const promptSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToPrompts(user.id, {\n                \"TerminalPromptManager.useEffect.promptSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" prompt\"));\n                    if (payload.eventType === 'INSERT') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id === payload.new.id ? payload.new : p\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setPrompts({\n                            \"TerminalPromptManager.useEffect.promptSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.promptSubscription\": (p)=>p.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.promptSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.promptSubscription\"]);\n            // Subscribe to folder changes\n            const folderSubscription = _lib_database__WEBPACK_IMPORTED_MODULE_4__.subscriptions.subscribeToFolders(user.id, {\n                \"TerminalPromptManager.useEffect.folderSubscription\": (payload)=>{\n                    addToLog(\"real-time update: \".concat(payload.eventType, \" folder\"));\n                    if (payload.eventType === 'INSERT') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.map({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id === payload.new.id ? payload.new : f\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setFolders({\n                            \"TerminalPromptManager.useEffect.folderSubscription\": (prev)=>prev.filter({\n                                    \"TerminalPromptManager.useEffect.folderSubscription\": (f)=>f.id !== payload.old.id\n                                }[\"TerminalPromptManager.useEffect.folderSubscription\"])\n                        }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n                    }\n                }\n            }[\"TerminalPromptManager.useEffect.folderSubscription\"]);\n            // Cleanup subscriptions\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>{\n                    promptSubscription.unsubscribe();\n                    folderSubscription.unsubscribe();\n                }\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], [\n        user\n    ]);\n    // Update time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TerminalPromptManager.useEffect\": ()=>{\n            const timer = setInterval({\n                \"TerminalPromptManager.useEffect.timer\": ()=>setCurrentTime(new Date())\n            }[\"TerminalPromptManager.useEffect.timer\"], 1000);\n            return ({\n                \"TerminalPromptManager.useEffect\": ()=>clearInterval(timer)\n            })[\"TerminalPromptManager.useEffect\"];\n        }\n    }[\"TerminalPromptManager.useEffect\"], []);\n    // Show loading screen while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-400 text-lg mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect to login if not authenticated\n    if (!user) {\n        return null;\n    }\n    // Get all available categories (default + custom)\n    const allCategories = [\n        \"general\",\n        \"development\",\n        \"communication\",\n        \"analysis\",\n        \"creative\",\n        ...userSettings.customCategories\n    ];\n    // Advanced sorting function\n    const sortPrompts = (prompts)=>{\n        return prompts.sort((a, b)=>{\n            // 1. Always prioritize pinned prompts\n            if (a.is_pinned && !b.is_pinned) return -1;\n            if (!a.is_pinned && b.is_pinned) return 1;\n            // 2. Apply secondary sorting\n            switch(sortBy){\n                case \"smart\":\n                    // Smart: Pin > Rating > Recent\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"rating\":\n                    // Rating: Pin > Rating > Title\n                    if (a.rating !== b.rating) return b.rating - a.rating;\n                    return a.title.localeCompare(b.title);\n                case \"date\":\n                    // Date: Pin > Recent > Old\n                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                case \"title\":\n                    // Title: Pin > Alphabetical\n                    return a.title.localeCompare(b.title);\n                default:\n                    return 0;\n            }\n        });\n    };\n    const filteredPrompts = sortPrompts(prompts.filter((prompt)=>{\n        var _prompt_folderInfo;\n        const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) || prompt.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || prompt.category === selectedCategory;\n        // Folder filtering\n        const matchesFolder = selectedFolder === null || ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) === selectedFolder;\n        return matchesSearch && matchesCategory && matchesFolder;\n    }));\n    const addToLog = (message)=>{\n        setActivityLog((prev)=>[\n                ...prev.slice(-4),\n                \"user@promptmanager: \".concat(message)\n            ]);\n    };\n    const copyPrompt = async (prompt)=>{\n        try {\n            await navigator.clipboard.writeText(prompt.content);\n            // Update last used timestamp\n            if (user) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.updateLastUsed(prompt.id, user.id);\n                loadPrompts() // Refresh the list\n                ;\n            }\n            addToLog('copied \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog('failed to copy \"'.concat(prompt.title, '\"'));\n        }\n    };\n    const togglePin = async (id)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === id);\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.togglePin(id, user.id);\n            // Preserve folder info when updating\n            if (prompt && prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === id ? updatedPrompt : p));\n            addToLog(\"\".concat(updatedPrompt.is_pinned ? \"pinned\" : \"unpinned\", ' \"').concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to toggle pin: \".concat(err.message));\n        }\n    };\n    const updateRating = async (promptId, rating)=>{\n        if (!user) return;\n        try {\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(promptId, {\n                rating\n            }, user.id);\n            // Preserve folder info when updating\n            if (prompt.folderInfo) {\n                updatedPrompt.folderInfo = prompt.folderInfo;\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === promptId ? updatedPrompt : p));\n            addToLog('rated prompt \"'.concat(updatedPrompt.title, '\" ').concat(rating, \" stars\"));\n        } catch (err) {\n            addToLog(\"failed to update rating: \".concat(err.message));\n        }\n    };\n    const addCustomCategory = (categoryName)=>{\n        const category = categoryName.trim().toLowerCase();\n        if (category && !allCategories.includes(category)) {\n            // Add to user settings\n            const updatedSettings = {\n                ...userSettings,\n                customCategories: [\n                    ...userSettings.customCategories,\n                    category\n                ]\n            };\n            setUserSettings(updatedSettings);\n            // Save to localStorage immediately\n            localStorage.setItem('promptManagerSettings', JSON.stringify(updatedSettings));\n            addToLog('added custom category \"'.concat(category, '\"'));\n            return category;\n        }\n        return null;\n    };\n    const movePromptToFolder = async (promptId, targetFolderId)=>{\n        if (!user) return;\n        try {\n            var _prompt_folderInfo, _folders_find;\n            const prompt = prompts.find((p)=>p.id === promptId);\n            if (!prompt) return;\n            const currentFolderId = ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || null;\n            // Don't do anything if dropping in the same folder\n            if (currentFolderId === targetFolderId) return;\n            // Update database relationship\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(promptId, currentFolderId, targetFolderId);\n            // Update local state\n            setPrompts((prev)=>prev.map((p)=>{\n                    if (p.id === promptId) {\n                        const updatedPrompt = {\n                            ...p\n                        };\n                        if (targetFolderId) {\n                            const targetFolder = folders.find((f)=>f.id === targetFolderId);\n                            if (targetFolder) {\n                                updatedPrompt.folderInfo = {\n                                    id: targetFolder.id,\n                                    name: targetFolder.name,\n                                    color: targetFolder.color\n                                };\n                            }\n                        } else {\n                            delete updatedPrompt.folderInfo;\n                        }\n                        return updatedPrompt;\n                    }\n                    return p;\n                }));\n            const targetFolderName = targetFolderId ? ((_folders_find = folders.find((f)=>f.id === targetFolderId)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"folder\" : \"no folder\";\n            addToLog('moved \"'.concat(prompt.title, '\" to ').concat(targetFolderName));\n        } catch (err) {\n            addToLog(\"failed to move prompt: \".concat(err.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            addToLog(\"signed out\");\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Test database connectivity\n    const testDatabase = async ()=>{\n        if (!user) return;\n        try {\n            console.log('Testing database connectivity...');\n            // Test prompts table\n            const prompts = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.getAll(user.id);\n            console.log('Prompts loaded:', prompts.length);\n            // Test folders table\n            const folders = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.getAll(user.id);\n            console.log('Folders loaded:', folders.length);\n            // Test prompt_folders table if we have prompts\n            if (prompts.length > 0) {\n                const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompts[0].id);\n                console.log('Folder relationship test:', folder);\n            }\n            addToLog(\"database test completed - check console\");\n        } catch (err) {\n            console.error('Database test failed:', err);\n            addToLog(\"database test failed: \".concat(err.message));\n        }\n    };\n    const createPrompt = async ()=>{\n        if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return;\n        try {\n            setIsCreatingPrompt(true);\n            // Add default tags based on category if no tags provided\n            let tags = newPrompt.tags;\n            if (tags.length === 0) {\n                const defaultTags = {\n                    development: [\n                        'code',\n                        'programming'\n                    ],\n                    communication: [\n                        'email',\n                        'writing'\n                    ],\n                    analysis: [\n                        'data',\n                        'insights'\n                    ],\n                    creative: [\n                        'brainstorm',\n                        'ideas'\n                    ],\n                    general: [\n                        'prompt'\n                    ]\n                };\n                tags = defaultTags[newPrompt.category] || [\n                    'prompt'\n                ];\n            }\n            const prompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.create({\n                title: newPrompt.title.trim(),\n                content: newPrompt.content.trim(),\n                category: newPrompt.category,\n                tags: tags,\n                user_id: user.id\n            });\n            // Add to folder if selected\n            if (newPrompt.folderId !== \"none\") {\n                try {\n                    console.log('Adding prompt to folder:', {\n                        promptId: prompt.id,\n                        folderId: newPrompt.folderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId);\n                    const selectedFolder = folders.find((f)=>f.id === newPrompt.folderId);\n                    if (selectedFolder) {\n                        // Store folder info in the prompt object for display\n                        prompt.folderInfo = {\n                            id: selectedFolder.id,\n                            name: selectedFolder.name,\n                            color: selectedFolder.color\n                        };\n                        console.log('Added folder info to new prompt:', selectedFolder.name);\n                    }\n                } catch (err) {\n                    console.error('Failed to add prompt to folder:', err);\n                    addToLog(\"failed to add to folder: \".concat(err.message));\n                }\n            }\n            setPrompts((prev)=>[\n                    prompt,\n                    ...prev\n                ]);\n            setNewPrompt({\n                title: \"\",\n                content: \"\",\n                category: userSettings.defaultCategory,\n                tags: [],\n                folderId: \"none\"\n            });\n            setIsAddingCustomCategory(false);\n            setCustomCategoryInput(\"\");\n            setIsNewPromptDialogOpen(false) // Close the dialog\n            ;\n            addToLog('created prompt \"'.concat(prompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create prompt: \".concat(err.message));\n        } finally{\n            setIsCreatingPrompt(false);\n        }\n    };\n    const createFolder = async ()=>{\n        if (!user || !newFolderName.trim()) return;\n        try {\n            setIsCreatingFolder(true);\n            const colors = [\n                'bg-blue-500',\n                'bg-green-500',\n                'bg-purple-500',\n                'bg-orange-500',\n                'bg-red-500',\n                'bg-yellow-500'\n            ];\n            const randomColor = colors[Math.floor(Math.random() * colors.length)];\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.folderService.create({\n                name: newFolderName.trim(),\n                color: randomColor,\n                user_id: user.id\n            });\n            setFolders((prev)=>[\n                    folder,\n                    ...prev\n                ]);\n            setNewFolderName(\"\");\n            setIsNewFolderDialogOpen(false);\n            addToLog('created folder \"'.concat(folder.name, '\"'));\n        } catch (err) {\n            addToLog(\"failed to create folder: \".concat(err.message));\n        } finally{\n            setIsCreatingFolder(false);\n        }\n    };\n    const openEditDialog = async (prompt)=>{\n        try {\n            // Load current folder relationship from database\n            const folder = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.getFolderForPrompt(prompt.id);\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: (folder === null || folder === void 0 ? void 0 : folder.id) || \"none\",\n                folderInfo: folder ? {\n                    id: folder.id,\n                    name: folder.name,\n                    color: folder.color\n                } : undefined\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        } catch (err) {\n            var _prompt_folderInfo;\n            console.error('Failed to load folder for editing:', err);\n            // Fallback to current folder info\n            const promptWithFolder = {\n                ...prompt,\n                currentFolderId: ((_prompt_folderInfo = prompt.folderInfo) === null || _prompt_folderInfo === void 0 ? void 0 : _prompt_folderInfo.id) || \"none\"\n            };\n            setEditingPrompt(promptWithFolder);\n            setIsEditDialogOpen(true);\n        }\n    };\n    const updatePrompt = async ()=>{\n        if (!user || !editingPrompt) return;\n        try {\n            var _editingPrompt_folderInfo;\n            const updatedPrompt = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptService.update(editingPrompt.id, {\n                title: editingPrompt.title,\n                content: editingPrompt.content,\n                category: editingPrompt.category,\n                tags: editingPrompt.tags\n            }, user.id);\n            // Update folder relationship if changed\n            const newFolderId = editingPrompt.currentFolderId;\n            const oldFolderId = ((_editingPrompt_folderInfo = editingPrompt.folderInfo) === null || _editingPrompt_folderInfo === void 0 ? void 0 : _editingPrompt_folderInfo.id) || null;\n            console.log('Folder update check:', {\n                newFolderId,\n                oldFolderId,\n                comparison: newFolderId !== (oldFolderId || \"none\")\n            });\n            if (newFolderId !== (oldFolderId || \"none\")) {\n                try {\n                    console.log('Updating folder relationship:', {\n                        promptId: updatedPrompt.id,\n                        oldFolderId,\n                        newFolderId\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_4__.promptFolderService.updatePromptFolder(updatedPrompt.id, oldFolderId, newFolderId !== \"none\" ? newFolderId : null);\n                    console.log('Folder relationship updated successfully');\n                    // Update folder info for display\n                    if (newFolderId !== \"none\") {\n                        const selectedFolder = folders.find((f)=>f.id === newFolderId);\n                        if (selectedFolder) {\n                            updatedPrompt.folderInfo = {\n                                id: selectedFolder.id,\n                                name: selectedFolder.name,\n                                color: selectedFolder.color\n                            };\n                            console.log('Added folder info to prompt:', selectedFolder.name);\n                        }\n                    } else {\n                        delete updatedPrompt.folderInfo;\n                        console.log('Removed folder info from prompt');\n                    }\n                } catch (err) {\n                    console.error('Failed to update folder relationship:', err);\n                    addToLog(\"failed to update folder: \".concat(err.message));\n                }\n            } else {\n                console.log('No folder change needed');\n            }\n            setPrompts((prev)=>prev.map((p)=>p.id === updatedPrompt.id ? updatedPrompt : p));\n            setIsEditDialogOpen(false);\n            setEditingPrompt(null);\n            addToLog('updated prompt \"'.concat(updatedPrompt.title, '\"'));\n        } catch (err) {\n            addToLog(\"failed to update prompt: \".concat(err.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 px-6 py-3 border-b border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity\",\n                                            onClick: ()=>{\n                                                setSelectedFolder(null);\n                                                setSearchTerm(\"\");\n                                                setSelectedCategory(\"all\");\n                                                setSortBy(\"smart\");\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-black text-2xl terminal-glow select-none\",\n                                                    children: [\n                                                        \">\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"_\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold text-lg terminal-glow\",\n                                                    children: \"PROMPT MANAGER\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Folders\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsSettingsOpen(true),\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-400 hover:text-green-300 text-sm\",\n                                                    children: \"Help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"text-green-400 hover:text-green-300 text-sm flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-sm\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-b border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"user@promptmanager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-300\",\n                                            children: currentPath\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"_\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Online\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-[calc(100vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 bg-gray-900 border-r border-gray-700 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> FOLDERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === 'all' ? 'bg-green-800/30 border-green-400' : ''),\n                                                            onClick: ()=>setSelectedFolder(null),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder('all');\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, null);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded bg-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-300 text-sm flex-1\",\n                                                                    children: \"All Folders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 text-xs\",\n                                                                    children: prompts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors \".concat(selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : '', \" \").concat(dragOverFolder === folder.id ? 'bg-green-800/30 border-green-400 border' : ''),\n                                                                onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                                onDragOver: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.dataTransfer.dropEffect = 'move';\n                                                                    setDragOverFolder(folder.id);\n                                                                },\n                                                                onDragLeave: ()=>setDragOverFolder(null),\n                                                                onDrop: (e)=>{\n                                                                    e.preventDefault();\n                                                                    if (draggedPrompt) {\n                                                                        movePromptToFolder(draggedPrompt.id, folder.id);\n                                                                    }\n                                                                    setDragOverFolder(null);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded \".concat(folder.color)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-300 text-sm flex-1\",\n                                                                        children: folder.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-500 text-xs\",\n                                                                        children: folder.promptCount || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, folder.id, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-bold mb-2 terminal-glow\",\n                                                    children: \"> ACTIONS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewPromptDialogOpen,\n                                                            onOpenChange: (open)=>{\n                                                                setIsNewPromptDialogOpen(open);\n                                                                if (!open) {\n                                                                    setIsAddingCustomCategory(false);\n                                                                    setCustomCategoryInput(\"\");\n                                                                }\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Prompt\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Prompt title...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newPrompt.title,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                title: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 801,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                    placeholder: \"Prompt content...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                    value: newPrompt.content,\n                                                                                    onChange: (e)=>setNewPrompt((prev)=>({\n                                                                                                ...prev,\n                                                                                                content: e.target.value\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 807,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Category (Type of prompt)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 814,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        !isAddingCustomCategory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                                value: newPrompt.category,\n                                                                                                onValueChange: (value)=>{\n                                                                                                    if (value === \"add_custom\") {\n                                                                                                        setIsAddingCustomCategory(true);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    } else {\n                                                                                                        setNewPrompt((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                category: value\n                                                                                                            }));\n                                                                                                    }\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                            placeholder: \"Select category\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 829,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 828,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                        children: [\n                                                                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                    value: category,\n                                                                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                                }, category, false, {\n                                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                    lineNumber: 833,\n                                                                                                                    columnNumber: 39\n                                                                                                                }, this)),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: \"add_custom\",\n                                                                                                                className: \"text-blue-400 hover:bg-gray-700 font-semibold\",\n                                                                                                                children: \"+ Add Custom Category\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 837,\n                                                                                                                columnNumber: 37\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 831,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 817,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 816,\n                                                                                            columnNumber: 31\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                    placeholder: \"Enter custom category...\",\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                    value: customCategoryInput,\n                                                                                                    onChange: (e)=>setCustomCategoryInput(e.target.value),\n                                                                                                    onKeyPress: (e)=>{\n                                                                                                        if (e.key === 'Enter' && customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    autoFocus: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 845,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    onClick: ()=>{\n                                                                                                        if (customCategoryInput.trim()) {\n                                                                                                            const newCategory = addCustomCategory(customCategoryInput);\n                                                                                                            if (newCategory) {\n                                                                                                                setNewPrompt((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        category: newCategory\n                                                                                                                    }));\n                                                                                                                setIsAddingCustomCategory(false);\n                                                                                                                setCustomCategoryInput(\"\");\n                                                                                                            }\n                                                                                                        }\n                                                                                                    },\n                                                                                                    className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                    children: \"Add\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 862,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                    size: \"sm\",\n                                                                                                    variant: \"outline\",\n                                                                                                    onClick: ()=>{\n                                                                                                        setIsAddingCustomCategory(false);\n                                                                                                        setCustomCategoryInput(\"\");\n                                                                                                    },\n                                                                                                    className: \"border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 878,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 844,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 813,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-green-400 text-xs mb-1 block\",\n                                                                                            children: \"Folder (Organization)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 893,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                            value: newPrompt.folderId,\n                                                                                            onValueChange: (value)=>setNewPrompt((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        folderId: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                                                                        placeholder: \"Select folder (optional)\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 899,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 898,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: \"none\",\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: \"No folder\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 902,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                                value: folder.id,\n                                                                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                                children: folder.name\n                                                                                                            }, folder.id, false, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 904,\n                                                                                                                columnNumber: 33\n                                                                                                            }, this))\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 901,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                            lineNumber: 894,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 892,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createPrompt,\n                                                                                    disabled: isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim(),\n                                                                                    children: isCreatingPrompt ? \"Creating...\" : \"Create Prompt\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 915,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isNewFolderDialogOpen,\n                                                            onOpenChange: setIsNewFolderDialogOpen,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                                        children: \"+ New Folder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 927,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                    className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                                className: \"text-green-400\",\n                                                                                children: \"Create New Folder\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 933,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 932,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                    placeholder: \"Folder name...\",\n                                                                                    className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                    value: newFolderName,\n                                                                                    onChange: (e)=>setNewFolderName(e.target.value)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 936,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                    onClick: createFolder,\n                                                                                    disabled: isCreatingFolder || !newFolderName.trim(),\n                                                                                    children: isCreatingFolder ? \"Creating...\" : \"Create Folder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                    lineNumber: 942,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 935,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 931,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isEditDialogOpen,\n                                                            onOpenChange: setIsEditDialogOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"Edit Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    editingPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                placeholder: \"Prompt title...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                value: editingPrompt.title,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            title: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                placeholder: \"Prompt content...\",\n                                                                                className: \"bg-gray-900 border-gray-600 text-green-300 h-32\",\n                                                                                value: editingPrompt.content,\n                                                                                onChange: (e)=>setEditingPrompt((prev)=>prev ? {\n                                                                                            ...prev,\n                                                                                            content: e.target.value\n                                                                                        } : null)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Category (Type of prompt)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 974,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.category,\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    category: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 980,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 979,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 984,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 982,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 975,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 973,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Folder (Organization)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 992,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: editingPrompt.currentFolderId || \"none\",\n                                                                                        onValueChange: (value)=>setEditingPrompt((prev)=>prev ? {\n                                                                                                    ...prev,\n                                                                                                    currentFolderId: value\n                                                                                                } : null),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 998,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 997,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: \"none\",\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: \"No folder\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1001,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                            value: folder.id,\n                                                                                                            className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                            children: folder.name\n                                                                                                        }, folder.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                            lineNumber: 1003,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1000,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 993,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 991,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                                                onClick: updatePrompt,\n                                                                                children: \"Update Prompt\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1014,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                            open: isSettingsOpen,\n                                                            onOpenChange: setIsSettingsOpen,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                                className: \"bg-gray-800 border-gray-600 text-green-300 max-w-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                            className: \"text-green-400\",\n                                                                            children: \"User Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1029,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1028,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Display Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1033,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                        placeholder: \"Your display name...\",\n                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                        value: userSettings.displayName,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    displayName: e.target.value\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1034,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1032,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"text-green-400 text-xs mb-1 block\",\n                                                                                        children: \"Default Category for New Prompts\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1043,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                                                        value: userSettings.defaultCategory,\n                                                                                        onValueChange: (value)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    defaultCategory: value\n                                                                                                })),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                                                                className: \"bg-gray-900 border-gray-600 text-green-300\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                    lineNumber: 1049,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1048,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                                                                className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                                                                children: allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                                                        value: category,\n                                                                                                        className: \"text-green-300 hover:bg-gray-700\",\n                                                                                                        children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                                                    }, category, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1053,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1051,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1044,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1042,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        id: \"autoSave\",\n                                                                                        checked: userSettings.autoSave,\n                                                                                        onChange: (e)=>setUserSettings((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    autoSave: e.target.checked\n                                                                                                })),\n                                                                                        className: \"rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1062,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: \"autoSave\",\n                                                                                        className: \"text-green-300 text-sm\",\n                                                                                        children: \"Auto-save prompts while typing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1069,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1061,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Custom Categories\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1075,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex space-x-2\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                                        placeholder: \"Add custom category...\",\n                                                                                                        className: \"bg-gray-900 border-gray-600 text-green-300 flex-1\",\n                                                                                                        value: newCustomCategory,\n                                                                                                        onChange: (e)=>setNewCustomCategory(e.target.value),\n                                                                                                        onKeyPress: (e)=>{\n                                                                                                            if (e.key === 'Enter' && newCustomCategory.trim()) {\n                                                                                                                const category = newCustomCategory.trim().toLowerCase();\n                                                                                                                if (!allCategories.includes(category)) {\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: [\n                                                                                                                                ...prev.customCategories,\n                                                                                                                                category\n                                                                                                                            ]\n                                                                                                                        }));\n                                                                                                                    setNewCustomCategory(\"\");\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1078,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                                        size: \"sm\",\n                                                                                                        onClick: ()=>{\n                                                                                                            const category = newCustomCategory.trim().toLowerCase();\n                                                                                                            if (category && !allCategories.includes(category)) {\n                                                                                                                setUserSettings((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        customCategories: [\n                                                                                                                            ...prev.customCategories,\n                                                                                                                            category\n                                                                                                                        ]\n                                                                                                                    }));\n                                                                                                                setNewCustomCategory(\"\");\n                                                                                                            }\n                                                                                                        },\n                                                                                                        className: \"bg-green-600 hover:bg-green-700\",\n                                                                                                        children: \"Add\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1096,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1077,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            userSettings.customCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"space-y-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-green-500\",\n                                                                                                        children: \"Your custom categories:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1116,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                                        children: userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                                                                variant: \"outline\",\n                                                                                                                className: \"text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    setUserSettings((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            customCategories: prev.customCategories.filter((c)=>c !== category)\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: [\n                                                                                                                    category.charAt(0).toUpperCase() + category.slice(1),\n                                                                                                                    \" \\xd7\"\n                                                                                                                ]\n                                                                                                            }, category, true, {\n                                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                                lineNumber: 1119,\n                                                                                                                columnNumber: 39\n                                                                                                            }, this))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1117,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"text-xs text-gray-500\",\n                                                                                                        children: \"Click to remove\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                        lineNumber: 1134,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1115,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1076,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1074,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"border-t border-gray-700 pt-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-green-400 text-sm font-semibold mb-2\",\n                                                                                        children: \"Account Information\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1141,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-xs text-green-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Email: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.email\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1143,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"User ID: \",\n                                                                                                    user === null || user === void 0 ? void 0 : user.id\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1144,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    \"Member since: \",\n                                                                                                    (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString() : 'N/A'\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                                lineNumber: 1145,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1142,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1140,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        className: \"flex-1 bg-green-600 hover:bg-green-700\",\n                                                                                        onClick: ()=>{\n                                                                                            // Save settings to localStorage for now\n                                                                                            localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings));\n                                                                                            setIsSettingsOpen(false);\n                                                                                            addToLog(\"settings saved\");\n                                                                                        },\n                                                                                        children: \"Save Settings\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1150,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"flex-1 border-gray-600 text-green-300 hover:bg-gray-700\",\n                                                                                        onClick: ()=>setIsSettingsOpen(false),\n                                                                                        children: \"Cancel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1161,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1149,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1031,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded\",\n                                                            children: \"Import/Export\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-gray-800 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-green-400 text-lg font-bold terminal-glow\",\n                                                        children: selectedFolder === null ? \"All Prompts\" : ((_folders_find = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find === void 0 ? void 0 : _folders_find.name) || \"Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500 text-sm\",\n                                                        children: [\n                                                            \"(\",\n                                                            filteredPrompts.length,\n                                                            \" prompts)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedFolder !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Show All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1195,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: testDatabase,\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                        children: \"Test DB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ New Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1205,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        placeholder: \"Search prompts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: selectedCategory,\n                                                onValueChange: setSelectedCategory,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-48 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"all\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"All Categories\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1238,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"general\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1239,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"development\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Development\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"communication\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Communication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"analysis\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"creative\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Creative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            userSettings.customCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                    value: category,\n                                                                    className: \"text-green-300 hover:bg-gray-700\",\n                                                                    children: category.charAt(0).toUpperCase() + category.slice(1)\n                                                                }, category, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                                value: sortBy,\n                                                onValueChange: setSortBy,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                        className: \"w-40 bg-gray-900 border-gray-600 text-green-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1254,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                        className: \"bg-gray-800 border-gray-600 text-green-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"smart\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"Smart Sort\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1257,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"rating\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1258,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"date\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: \"title\",\n                                                                className: \"text-green-300 hover:bg-gray-700\",\n                                                                children: \"By Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1260,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 15\n                                    }, this),\n                                    folders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-green-400 text-sm font-semibold\",\n                                                    children: \"Quick Access Folders:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 \".concat(dragOverFolder === 'all' ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50', \" \").concat(selectedFolder === null ? 'bg-green-900/30 border-green-500' : ''),\n                                                        onClick: ()=>setSelectedFolder(null),\n                                                        onDragOver: (e)=>{\n                                                            e.preventDefault();\n                                                            e.dataTransfer.dropEffect = 'move';\n                                                            setDragOverFolder('all');\n                                                        },\n                                                        onDragLeave: ()=>setDragOverFolder(null),\n                                                        onDrop: (e)=>{\n                                                            e.preventDefault();\n                                                            if (draggedPrompt) {\n                                                                movePromptToFolder(draggedPrompt.id, null);\n                                                            }\n                                                            setDragOverFolder(null);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1292,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-300 text-center font-medium\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-500\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    prompts.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 \".concat(dragOverFolder === folder.id ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50', \" \").concat(selectedFolder === folder.id ? 'bg-green-900/30 border-green-500' : ''),\n                                                            onClick: ()=>setSelectedFolder(selectedFolder === folder.id ? null : folder.id),\n                                                            onDragOver: (e)=>{\n                                                                e.preventDefault();\n                                                                e.dataTransfer.dropEffect = 'move';\n                                                                setDragOverFolder(folder.id);\n                                                            },\n                                                            onDragLeave: ()=>setDragOverFolder(null),\n                                                            onDrop: (e)=>{\n                                                                e.preventDefault();\n                                                                if (draggedPrompt) {\n                                                                    movePromptToFolder(draggedPrompt.id, folder.id);\n                                                                }\n                                                                setDragOverFolder(null);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 \".concat(folder.color, \" rounded-lg flex items-center justify-center mb-2\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1321,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-300 text-center font-medium max-w-16 truncate\",\n                                                                    children: folder.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1324,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        folder.promptCount || 0,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                    lineNumber: 1327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, folder.id, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                            lineNumber: 1301,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1267,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: loadingPrompts ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1338,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Loading prompts...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1337,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-full text-center text-green-400 py-8\",\n                                            children: [\n                                                selectedFolder !== null ? 'No prompts in \"'.concat((_folders_find1 = folders.find((f)=>f.id === selectedFolder)) === null || _folders_find1 === void 0 ? void 0 : _folders_find1.name, '\" folder yet.') : searchTerm || selectedCategory !== \"all\" ? \"No prompts match your search.\" : \"No prompts yet. Create your first prompt!\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        onClick: ()=>setIsNewPromptDialogOpen(true),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white\",\n                                                        children: \"+ Create First Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1342,\n                                            columnNumber: 19\n                                        }, this) : filteredPrompts.map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                draggable: true,\n                                                onDragStart: (e)=>{\n                                                    setDraggedPrompt(prompt);\n                                                    e.dataTransfer.effectAllowed = 'move';\n                                                    e.dataTransfer.setData('text/plain', prompt.id);\n                                                    // Add visual feedback\n                                                    e.currentTarget.style.opacity = '0.5';\n                                                },\n                                                onDragEnd: (e)=>{\n                                                    setDraggedPrompt(null);\n                                                    setDragOverFolder(null);\n                                                    e.currentTarget.style.opacity = '1';\n                                                },\n                                                className: \"bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move \".concat(prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 opacity-30 hover:opacity-60 transition-opacity\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-green-400 text-xs\",\n                                                                            children: \"⋮⋮\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1382,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                        className: \"text-green-400 text-sm font-bold\",\n                                                                        children: prompt.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            prompt.is_pinned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1386,\n                                                                                columnNumber: 48\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    ...Array(5)\n                                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"w-3 h-3 cursor-pointer transition-colors hover:text-green-300 \".concat(i < prompt.rating ? \"text-green-400 fill-current\" : \"text-gray-600 hover:text-gray-400\"),\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            const newRating = i + 1;\n                                                                                            // If clicking the same star that's already the rating, clear it\n                                                                                            if (newRating === prompt.rating) {\n                                                                                                updateRating(prompt.id, 0);\n                                                                                            } else {\n                                                                                                updateRating(prompt.id, newRating);\n                                                                                            }\n                                                                                        }\n                                                                                    }, i, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                        lineNumber: 1389,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                                lineNumber: 1387,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-blue-400 border-blue-500 bg-blue-900/20\",\n                                                                        children: prompt.category.toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1410,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.folderInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs text-purple-400 border-purple-500 bg-purple-900/20\",\n                                                                        children: prompt.folderInfo.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1414,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    prompt.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs text-green-500 border-green-600 bg-green-900/20\",\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1419,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1409,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1379,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-300 text-xs mb-3 line-clamp-3\",\n                                                                children: prompt.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1426,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-green-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(prompt.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1428,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    prompt.last_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Used: \",\n                                                                            new Date(prompt.last_used).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1429,\n                                                                        columnNumber: 46\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1427,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>copyPrompt(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1432,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>togglePin(prompt.id),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1446,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1440,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>openEditDialog(prompt),\n                                                                        className: \"text-green-400 border-green-600 hover:bg-green-900\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                            lineNumber: 1454,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                        lineNumber: 1448,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                                lineNumber: 1431,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                        lineNumber: 1425,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, prompt.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1360,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                        lineNumber: 1335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                lineNumber: 1182,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 px-6 py-2 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-green-500 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: draggedPrompt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: [\n                                                    'Dragging \"',\n                                                    draggedPrompt.title,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1471,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: \"Drop on folder to move\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1477,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1478,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    filteredPrompts.length,\n                                                    \" prompts\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1479,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1480,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    folders.length,\n                                                    \" folders\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Copy_Edit_Folder_LogOut_Pin_Search_Star_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1486,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Last activity: \",\n                                                activityLog[activityLog.length - 1]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                            lineNumber: 1487,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                                    lineNumber: 1485,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                            lineNumber: 1467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                        lineNumber: 1466,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n                lineNumber: 656,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n            lineNumber: 654,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/experiment/Propmt manager/app/page.tsx\",\n        lineNumber: 653,\n        columnNumber: 5\n    }, this);\n}\n_s(TerminalPromptManager, \"N0Z76MMChFCD+V1lCWDLl7qRtJQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TerminalPromptManager;\nvar _c;\n$RefreshReg$(_c, \"TerminalPromptManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});