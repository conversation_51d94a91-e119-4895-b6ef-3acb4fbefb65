(()=>{var e={};e.id=769,e.ids=[769],e.modules={253:()=>{},267:()=>{},496:()=>{},659:(e,t,r)=>{Promise.resolve().then(r.bind(r,7858))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},907:(e,t,r)=>{Promise.resolve().then(r.bind(r,4224))},1622:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});var s=r(9054);r(267);var o=r(7858);let n={title:"Prompt Manager",description:"A terminal-style prompt manager with Supabase integration",generator:"v0.dev"};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:(0,s.jsx)(<PERSON><PERSON>,{children:e})})})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2392:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(9309).createBrowserClient)("https://hshhltlveiwwiojsvzws.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhzaGhsdGx2ZWl3d2lvanN2endzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MzM0OTQsImV4cCI6MjA2NjAwOTQ5NH0._2KoxkErkk56V-hhzt1bgll7k_iv-oa6KbOOahZgy20")},2521:(e,t,r)=>{Promise.resolve().then(r.bind(r,6846))},2793:(e,t,r)=>{Promise.resolve().then(r.bind(r,4636))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3648:()=>{},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4224:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>l});var s=r(9812),o=r(9837),n=r(2392);let i=(0,o.createContext)(void 0);function a(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,o.useState)(null),[a,l]=(0,o.useState)(null),[u,d]=(0,o.useState)(!0),p=async(e,t)=>{let{error:r}=await n.N.auth.signInWithPassword({email:e,password:t});if(r)throw r},c=async(e,t)=>{let{error:r}=await n.N.auth.signUp({email:e,password:t});if(r)throw r},m=async()=>{let{error:e}=await n.N.auth.signOut();if(e)throw e},h=async e=>{let{error:t}=await n.N.auth.signInWithOAuth({provider:e,options:{redirectTo:`${window.location.origin}/auth/callback`}});if(t)throw t};return(0,s.jsx)(i.Provider,{value:{user:t,session:a,loading:u,signIn:p,signUp:c,signOut:m,signInWithProvider:h},children:e})}},4567:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6099,23)),Promise.resolve().then(r.t.bind(r,8047,23)),Promise.resolve().then(r.t.bind(r,3475,23)),Promise.resolve().then(r.t.bind(r,4114,23)),Promise.resolve().then(r.t.bind(r,1158,23)),Promise.resolve().then(r.t.bind(r,4378,23)),Promise.resolve().then(r.t.bind(r,9883,23)),Promise.resolve().then(r.t.bind(r,994,23))},4631:e=>{"use strict";e.exports=require("tls")},4636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(9812);r(9837);var o=r(6954);function n(){return(0,o.useRouter)(),(0,s.jsx)("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"bg-gray-800 p-8 text-center",children:[(0,s.jsx)("div",{className:"text-green-400 text-lg mb-4",children:"Authenticating..."}),(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"})]})})}r(2392)},4735:e=>{"use strict";e.exports=require("events")},5003:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=5003,e.exports=t},5006:(e,t,r)=>{"use strict";function s(){return null}r.r(t),r.d(t,{default:()=>s})},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5945:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=r(7702),o=r(3409),n=r(8409),i=r.n(n),a=r(4102),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["auth",{children:["callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6846)),"/Users/<USER>/Desktop/experiment/Propmt manager/app/auth/callback/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1622)),"/Users/<USER>/Desktop/experiment/Propmt manager/app/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,5006)),"/Users/<USER>/Desktop/experiment/Propmt manager/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,2655,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,2,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,8907,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/experiment/Propmt manager/app/auth/callback/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/auth/callback/page",pathname:"/auth/callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4457,23)),Promise.resolve().then(r.t.bind(r,2833,23)),Promise.resolve().then(r.t.bind(r,8409,23)),Promise.resolve().then(r.t.bind(r,8744,23)),Promise.resolve().then(r.t.bind(r,5704,23)),Promise.resolve().then(r.t.bind(r,1500,23)),Promise.resolve().then(r.t.bind(r,2026,23)),Promise.resolve().then(r.t.bind(r,3756,23))},6836:()=>{},6846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(430).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/experiment/Propmt manager/app/auth/callback/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/experiment/Propmt manager/app/auth/callback/page.tsx","default")},6954:(e,t,r)=>{"use strict";var s=r(5362);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},7858:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(430);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/experiment/Propmt manager/contexts/AuthContext.tsx","useAuth");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/experiment/Propmt manager/contexts/AuthContext.tsx","AuthProvider")},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[141],()=>r(5945));module.exports=s})();