"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Search, Star, Copy, Edit, Pin, Clock, Terminal, LogOut, Folder, Trash2, Grid3X3, Grid2X2, LayoutGrid, List } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { promptService, folderService, promptFolderService, subscriptions } from "@/lib/database"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Prompt {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  rating: number
  is_pinned: boolean
  created_at: string
  updated_at: string
  last_used: string | null
  user_id: string
}

interface FolderType {
  id: string
  name: string
  color: string
  created_at: string
  updated_at: string
  user_id: string
  promptCount?: number
}

export default function TerminalPromptManager() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 text-center">
          <div className="text-green-400 text-lg mb-4">Loading...</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return null
  }





  return (
    <div className="min-h-screen bg-gray-900 p-0">
      <div className="w-full h-screen">
        <div className="bg-gray-800 h-screen overflow-hidden">
          <div className="bg-gray-700 px-6 py-3 border-b border-gray-600">
            <div className="flex items-center justify-between">
              <div className="text-green-400 font-bold text-lg">PROMPT MANAGER</div>
              <div className="text-green-400 text-sm">{user?.email}</div>
            </div>
          </div>
          <div className="p-6">
            <div className="text-green-400">Welcome to Prompt Manager!</div>
          </div>
        </div>
      </div>
    </div>
                                        setUserSettings(prev => ({
                                          ...prev,
                                          customCategories: [...prev.customCategories, category]
                                        }))
                                        setNewCustomCategory("")
                                      }
                                    }
                                  }}
                                />
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    const category = newCustomCategory.trim().toLowerCase()
                                    if (category && !allCategories.includes(category)) {
                                      setUserSettings(prev => ({
                                        ...prev,
                                        customCategories: [...prev.customCategories, category]
                                      }))
                                      setNewCustomCategory("")
                                    }
                                  }}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  Add
                                </Button>
                              </div>

                              {userSettings.customCategories.length > 0 && (
                                <div className="space-y-1">
                                  <div className="text-xs text-green-500">Your custom categories:</div>
                                  <div className="flex flex-wrap gap-1">
                                    {userSettings.customCategories.map((category) => (
                                      <Badge
                                        key={category}
                                        variant="outline"
                                        className="text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400"
                                        onClick={() => {
                                          setUserSettings(prev => ({
                                            ...prev,
                                            customCategories: prev.customCategories.filter(c => c !== category)
                                          }))
                                        }}
                                      >
                                        {category.charAt(0).toUpperCase() + category.slice(1)} ×
                                      </Badge>
                                    ))}
                                  </div>
                                  <div className="text-xs text-gray-500">Click to remove</div>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="border-t border-gray-700 pt-4">
                            <h4 className="text-green-400 text-sm font-semibold mb-2">Account Information</h4>
                            <div className="space-y-1 text-xs text-green-500">
                              <div>Email: {user?.email}</div>
                              <div>User ID: {user?.id}</div>
                              <div>Member since: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</div>
                            </div>
                          </div>

                          <div className="flex space-x-2">
                            <Button
                              className="flex-1 bg-green-600 hover:bg-green-700"
                              onClick={() => {
                                // Save settings to localStorage for now
                                localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings))
                                setIsSettingsOpen(false)
                                addToLog("settings saved")
                              }}
                            >
                              Save Settings
                            </Button>
                            <Button
                              variant="outline"
                              className="flex-1 border-gray-600 text-green-300 hover:bg-gray-700"
                              onClick={() => setIsSettingsOpen(false)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>

                    <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                      Import/Export
                    </button>
                  </div>
                </div>

                {/* Trash Bin */}
                <div className="mt-6">
                  <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; DELETE</h3>
                  <div
                    className={`flex items-center justify-center p-4 rounded-lg border-2 border-dashed transition-all duration-300 ${
                      dragOverTrash
                        ? 'border-red-500 bg-red-900/30 scale-110'
                        : 'border-gray-600 hover:border-red-400'
                    }`}
                    onDragOver={(e) => {
                      e.preventDefault()
                      e.dataTransfer.dropEffect = 'move'
                      setDragOverTrash(true)
                      setIsTrashOpen(true)
                    }}
                    onDragLeave={() => {
                      setDragOverTrash(false)
                      setIsTrashOpen(false)
                    }}
                    onDrop={(e) => {
                      e.preventDefault()
                      if (draggedPrompt) {
                        deletePrompt(draggedPrompt.id)
                      }
                      setDragOverTrash(false)
                      setIsTrashOpen(false)
                    }}
                  >
                    <div className={`transition-all duration-300 ${
                      isTrashOpen || dragOverTrash ? 'scale-125 text-red-400' : 'text-gray-500'
                    }`}>
                      <Trash2 className={`w-8 h-8 transition-all duration-300 ${
                        isTrashOpen || dragOverTrash ? 'animate-bounce' : ''
                      }`} />
                    </div>
                  </div>
                  <div className="text-center mt-2">
                    <span className={`text-xs transition-colors duration-300 ${
                      dragOverTrash ? 'text-red-400' : 'text-gray-500'
                    }`}>
                      {dragOverTrash ? 'Release to Delete' : 'Drop to Delete'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 bg-gray-800 p-6 overflow-y-auto">
              {/* Header with Add Prompt Button */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <h2 className="text-green-400 text-lg font-bold terminal-glow">
                    {selectedFolder === null
                      ? "All Prompts"
                      : folders.find(f => f.id === selectedFolder)?.name || "Folder"}
                  </h2>
                  <span className="text-green-500 text-sm">
                    ({filteredPrompts.length} prompts)
                  </span>
                  {selectedFolder !== null && (
                    <Button
                      onClick={() => setSelectedFolder(null)}
                      variant="outline"
                      size="sm"
                      className="text-green-400 border-green-600 hover:bg-green-900"
                    >
                      Show All
                    </Button>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={testDatabase}
                    variant="outline"
                    className="text-green-400 border-green-600 hover:bg-green-900"
                  >
                    Test DB
                  </Button>
                  <Button
                    onClick={() => setIsNewPromptDialogOpen(true)}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    + New Prompt
                  </Button>
                </div>
              </div>

              {/* Search and Filter Bar */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4" />
                  <Input
                    placeholder="Search prompts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48 bg-gray-900 border-gray-600 text-green-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                    <SelectItem value="all" className="text-green-300 hover:bg-gray-700">All Categories</SelectItem>
                    <SelectItem value="general" className="text-green-300 hover:bg-gray-700">General</SelectItem>
                    <SelectItem value="development" className="text-green-300 hover:bg-gray-700">Development</SelectItem>
                    <SelectItem value="communication" className="text-green-300 hover:bg-gray-700">Communication</SelectItem>
                    <SelectItem value="analysis" className="text-green-300 hover:bg-gray-700">Analysis</SelectItem>
                    <SelectItem value="creative" className="text-green-300 hover:bg-gray-700">Creative</SelectItem>
                    {userSettings.customCategories.map((category) => (
                      <SelectItem key={category} value={category} className="text-green-300 hover:bg-gray-700">
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40 bg-gray-900 border-gray-600 text-green-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                    <SelectItem value="smart" className="text-green-300 hover:bg-gray-700">Smart Sort</SelectItem>
                    <SelectItem value="rating" className="text-green-300 hover:bg-gray-700">By Rating</SelectItem>
                    <SelectItem value="date" className="text-green-300 hover:bg-gray-700">By Date</SelectItem>
                    <SelectItem value="title" className="text-green-300 hover:bg-gray-700">By Title</SelectItem>
                  </SelectContent>
                </Select>

                {/* Layout Selector */}
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 text-sm">View:</span>
                  <div className="flex items-center space-x-1 bg-gray-900 rounded-lg p-1">
                    <button
                      onClick={() => setLayoutMode('large')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'large' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="Large cards (3 per row)"
                    >
                      <Grid2X2 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setLayoutMode('medium')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'medium' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="Medium cards (4 per row)"
                    >
                      <LayoutGrid className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setLayoutMode('small')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'small' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="Small cards (5 per row)"
                    >
                      <Grid3X3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setLayoutMode('list')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'list' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="List view (compact)"
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Folder Icons */}
              {folders.length > 0 && (
                <div className="mb-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <h3 className="text-green-400 text-sm font-semibold">Quick Access Folders:</h3>
                  </div>
                  <div className="flex flex-wrap gap-3">
                    {/* All Folders Icon */}
                    <div
                      className={`flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 ${
                        dragOverFolder === 'all' ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50'
                      } ${selectedFolder === null ? 'bg-green-900/30 border-green-500' : ''}`}
                      onClick={() => setSelectedFolder(null)}
                      onDragOver={(e) => {
                        e.preventDefault()
                        e.dataTransfer.dropEffect = 'move'
                        setDragOverFolder('all')
                      }}
                      onDragLeave={() => setDragOverFolder(null)}
                      onDrop={(e) => {
                        e.preventDefault()
                        if (draggedPrompt) {
                          movePromptToFolder(draggedPrompt.id, null)
                        }
                        setDragOverFolder(null)
                      }}
                    >
                      <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-2">
                        <Folder className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-xs text-green-300 text-center font-medium">All</span>
                      <span className="text-xs text-green-500">({prompts.length})</span>
                    </div>

                    {/* Individual Folder Icons */}
                    {folders.map((folder) => (
                      <div
                        key={folder.id}
                        className={`flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 ${
                          dragOverFolder === folder.id ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50'
                        } ${selectedFolder === folder.id ? 'bg-green-900/30 border-green-500' : ''}`}
                        onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                        onDragOver={(e) => {
                          e.preventDefault()
                          e.dataTransfer.dropEffect = 'move'
                          setDragOverFolder(folder.id)
                        }}
                        onDragLeave={() => setDragOverFolder(null)}
                        onDrop={(e) => {
                          e.preventDefault()
                          if (draggedPrompt) {
                            movePromptToFolder(draggedPrompt.id, folder.id)
                          }
                          setDragOverFolder(null)
                        }}
                      >
                        <div className={`w-12 h-12 ${folder.color} rounded-lg flex items-center justify-center mb-2`}>
                          <Folder className="w-6 h-6 text-white" />
                        </div>
                        <span className="text-xs text-green-300 text-center font-medium max-w-16 truncate">
                          {folder.name}
                        </span>
                        <span className="text-xs text-green-500">({folder.promptCount || 0})</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Prompts Grid */}
              <div className={`grid gap-4 ${
                layoutMode === 'large' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                layoutMode === 'medium' ? 'grid-cols-1 md:grid-cols-3 lg:grid-cols-4' :
                layoutMode === 'small' ? 'grid-cols-1 md:grid-cols-4 lg:grid-cols-5' :
                'grid-cols-1'
              }`}
                {loadingPrompts ? (
                  <div className="col-span-full text-center text-green-400 py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4"></div>
                    Loading prompts...
                  </div>
                ) : filteredPrompts.length === 0 ? (
                  <div className="col-span-full text-center text-green-400 py-8">
                    {selectedFolder !== null
                      ? `No prompts in "${folders.find(f => f.id === selectedFolder)?.name}" folder yet.`
                      : searchTerm || selectedCategory !== "all"
                        ? "No prompts match your search."
                        : "No prompts yet. Create your first prompt!"
                    }
                    <div className="mt-4">
                      <Button
                        onClick={() => setIsNewPromptDialogOpen(true)}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        + Create First Prompt
                      </Button>
                    </div>
                  </div>
                ) : (
                  filteredPrompts.map((prompt) => (
                    layoutMode === 'list' ? (
                      // List View
                      <div
                        key={prompt.id}
                        draggable
                        onDragStart={(e) => {
                          setDraggedPrompt(prompt)
                          e.dataTransfer.effectAllowed = 'move'
                          e.dataTransfer.setData('text/plain', prompt.id)
                          e.currentTarget.style.opacity = '0.5'
                        }}
                        onDragEnd={(e) => {
                          setDraggedPrompt(null)
                          setDragOverFolder(null)
                          setDragOverTrash(false)
                          setIsTrashOpen(false)
                          e.currentTarget.style.opacity = '1'
                        }}
                        className={`flex items-center justify-between p-3 bg-gray-900 border border-gray-700 hover:border-green-500 rounded-lg transition-colors cursor-move ${
                          prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3 flex-1">
                          <div className="text-green-400 text-xs opacity-30">⋮⋮</div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-green-400 text-sm font-medium">{prompt.title}</h3>
                              {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                            </div>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                                {prompt.category.toUpperCase()}
                              </Badge>
                              {(prompt as any).folderInfo && (
                                <Badge variant="outline" className="text-xs text-purple-400 border-purple-500 bg-purple-900/20">
                                  {(prompt as any).folderInfo.name}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-3 h-3 cursor-pointer transition-colors hover:text-green-300 ${
                                  i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                }`}
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const newRating = i + 1
                                  if (newRating === prompt.rating) {
                                    updateRating(prompt.id, 0)
                                  } else {
                                    updateRating(prompt.id, newRating)
                                  }
                                }}
                              />
                            ))}
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyPrompt(prompt)}
                              className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openEditDialog(prompt)}
                              className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      // Card Views (Large, Medium, Small)
                      <Card
                        key={prompt.id}
                        draggable
                        onDragStart={(e) => {
                          setDraggedPrompt(prompt)
                          e.dataTransfer.effectAllowed = 'move'
                          e.dataTransfer.setData('text/plain', prompt.id)
                          e.currentTarget.style.opacity = '0.5'
                        }}
                        onDragEnd={(e) => {
                          setDraggedPrompt(null)
                          setDragOverFolder(null)
                          setDragOverTrash(false)
                          setIsTrashOpen(false)
                          e.currentTarget.style.opacity = '1'
                        }}
                        className={`bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move ${
                          prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                        }`}
                      >
                        <CardHeader className={layoutMode === 'small' ? 'pb-1' : 'pb-2'}>
                          <div className="flex items-start justify-between">
                            <div className="absolute top-2 right-2 opacity-30 hover:opacity-60 transition-opacity">
                              <div className="text-green-400 text-xs">⋮⋮</div>
                            </div>
                            <CardTitle className={`text-green-400 font-bold ${
                              layoutMode === 'small' ? 'text-xs' : 'text-sm'
                            }`}>{prompt.title}</CardTitle>
                            <div className="flex items-center space-x-1">
                              {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`w-3 h-3 cursor-pointer transition-colors hover:text-green-300 ${
                                      i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      const newRating = i + 1
                                      if (newRating === prompt.rating) {
                                        updateRating(prompt.id, 0)
                                      } else {
                                        updateRating(prompt.id, newRating)
                                      }
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                          {layoutMode !== 'small' && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                                {prompt.category.toUpperCase()}
                              </Badge>
                              {(prompt as any).folderInfo && (
                                <Badge variant="outline" className="text-xs text-purple-400 border-purple-500 bg-purple-900/20">
                                  {(prompt as any).folderInfo.name}
                                </Badge>
                              )}
                              {layoutMode === 'large' && prompt.tags.map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs text-green-500 border-green-600 bg-green-900/20">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </CardHeader>
                        <CardContent className={layoutMode === 'small' ? 'pt-0' : ''}>
                          {layoutMode !== 'small' && (
                            <p className={`text-green-300 text-xs mb-3 ${
                              layoutMode === 'medium' ? 'line-clamp-2' : 'line-clamp-3'
                            }`}>{prompt.content}</p>
                          )}
                          {layoutMode !== 'small' && (
                            <div className="flex items-center justify-between text-xs text-green-500 mb-3">
                              <span>{new Date(prompt.created_at).toLocaleDateString()}</span>
                              {layoutMode === 'large' && prompt.last_used && (
                                <span>Used: {new Date(prompt.last_used).toLocaleDateString()}</span>
                              )}
                            </div>
                          )}
                          <div className={`flex items-center space-x-2 ${
                            layoutMode === 'small' ? 'justify-center' : ''
                          }`}>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyPrompt(prompt)}
                              className={`text-green-400 border-green-600 hover:bg-green-900 ${
                                layoutMode === 'small' ? 'h-6 w-6 p-0' : ''
                              }`}
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => togglePin(prompt.id)}
                              className={`text-green-400 border-green-600 hover:bg-green-900 ${
                                layoutMode === 'small' ? 'h-6 w-6 p-0' : ''
                              }`}
                            >
                              <Pin className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openEditDialog(prompt)}
                              className={`text-green-400 border-green-600 hover:bg-green-900 ${
                                layoutMode === 'small' ? 'h-6 w-6 p-0' : ''
                              }`}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Status Bar */}
          <div className="bg-gray-900 px-6 py-2 border-t border-gray-700">
            <div className="flex items-center justify-between text-green-500 text-xs">
              <div className="flex items-center space-x-4">
                {draggedPrompt ? (
                  <>
                    <span className="text-blue-400">Dragging "{draggedPrompt.title}"</span>
                    <span>|</span>
                    <span className="text-blue-400">Drop on folder to move</span>
                    <span>|</span>
                    <span className="text-red-400">Drop on trash to delete</span>
                  </>
                ) : (
                  <>
                    <span>Ready</span>
                    <span>|</span>
                    <span>{filteredPrompts.length} prompts</span>
                    <span>|</span>
                    <span>{folders.length} folders</span>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-3 h-3" />
                <span>Last activity: {activityLog[activityLog.length - 1]}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
