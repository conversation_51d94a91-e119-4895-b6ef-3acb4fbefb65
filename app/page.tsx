"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON>, Star, Copy, Edit, Pin, Clock, Terminal, LogOut } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { promptService, folderService, subscriptions } from "@/lib/database"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Prompt {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  rating: number
  is_pinned: boolean
  created_at: string
  updated_at: string
  last_used: string | null
  user_id: string
}

interface FolderType {
  id: string
  name: string
  color: string
  created_at: string
  updated_at: string
  user_id: string
  promptCount?: number
}

export default function TerminalPromptManager() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()

  const [currentTime, setCurrentTime] = useState(new Date())
  const [currentPath, setCurrentPath] = useState("/prompts")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [folders, setFolders] = useState<FolderType[]>([])
  const [loadingPrompts, setLoadingPrompts] = useState(true)
  const [loadingFolders, setLoadingFolders] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newPrompt, setNewPrompt] = useState({
    title: "",
    content: "",
    category: "general",
    tags: [] as string[],
    folderId: ""
  })
  const [isCreatingPrompt, setIsCreatingPrompt] = useState(false)
  const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = useState(false)
  const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = useState(false)
  const [newFolderName, setNewFolderName] = useState("")
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)

  const [activityLog, setActivityLog] = useState([
    "user@promptmanager: initializing system...",
  ])

  // Load prompts from database
  const loadPrompts = async () => {
    if (!user) return

    try {
      setLoadingPrompts(true)
      const data = await promptService.getAll(user.id)
      setPrompts(data)
      addToLog(`loaded ${data.length} prompts`)
    } catch (err: any) {
      setError(err.message)
      addToLog(`error loading prompts: ${err.message}`)
    } finally {
      setLoadingPrompts(false)
    }
  }

  // Load folders from database
  const loadFolders = async () => {
    if (!user) return

    try {
      setLoadingFolders(true)
      const data = await folderService.getAll(user.id)
      setFolders(data)
      addToLog(`loaded ${data.length} folders`)
    } catch (err: any) {
      setError(err.message)
      addToLog(`error loading folders: ${err.message}`)
    } finally {
      setLoadingFolders(false)
    }
  }

  // Authentication check
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  // Load data when user is authenticated
  useEffect(() => {
    if (user) {
      loadPrompts()
      loadFolders()
      addToLog("system ready")
    }
  }, [user])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return

    // Subscribe to prompt changes
    const promptSubscription = subscriptions.subscribeToPrompts(user.id, (payload) => {
      addToLog(`real-time update: ${payload.eventType} prompt`)

      if (payload.eventType === 'INSERT') {
        setPrompts(prev => [payload.new, ...prev])
      } else if (payload.eventType === 'UPDATE') {
        setPrompts(prev => prev.map(p => p.id === payload.new.id ? payload.new : p))
      } else if (payload.eventType === 'DELETE') {
        setPrompts(prev => prev.filter(p => p.id !== payload.old.id))
      }
    })

    // Subscribe to folder changes
    const folderSubscription = subscriptions.subscribeToFolders(user.id, (payload) => {
      addToLog(`real-time update: ${payload.eventType} folder`)

      if (payload.eventType === 'INSERT') {
        setFolders(prev => [payload.new, ...prev])
      } else if (payload.eventType === 'UPDATE') {
        setFolders(prev => prev.map(f => f.id === payload.new.id ? payload.new : f))
      } else if (payload.eventType === 'DELETE') {
        setFolders(prev => prev.filter(f => f.id !== payload.old.id))
      }
    })

    // Cleanup subscriptions
    return () => {
      promptSubscription.unsubscribe()
      folderSubscription.unsubscribe()
    }
  }, [user])

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 text-center">
          <div className="text-green-400 text-lg mb-4">Loading...</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return null
  }

  const filteredPrompts = prompts.filter((prompt) => {
    const matchesSearch =
      prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prompt.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || prompt.category === selectedCategory
    // For now, we'll just use category filtering since we don't have folder relationships implemented yet
    // TODO: Add folder filtering when prompt_folders relationship is implemented
    return matchesSearch && matchesCategory
  })

  const addToLog = (message: string) => {
    setActivityLog((prev) => [...prev.slice(-4), `user@promptmanager: ${message}`])
  }

  const copyPrompt = async (prompt: Prompt) => {
    try {
      await navigator.clipboard.writeText(prompt.content)
      // Update last used timestamp
      if (user) {
        await promptService.updateLastUsed(prompt.id, user.id)
        loadPrompts() // Refresh the list
      }
      addToLog(`copied "${prompt.title}"`)
    } catch (err) {
      addToLog(`failed to copy "${prompt.title}"`)
    }
  }

  const togglePin = async (id: string) => {
    if (!user) return

    try {
      const updatedPrompt = await promptService.togglePin(id, user.id)
      setPrompts((prev) => prev.map((p) => (p.id === id ? updatedPrompt : p)))
      addToLog(`${updatedPrompt.is_pinned ? "pinned" : "unpinned"} "${updatedPrompt.title}"`)
    } catch (err: any) {
      addToLog(`failed to toggle pin: ${err.message}`)
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      addToLog("signed out")
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const createPrompt = async () => {
    if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return

    try {
      setIsCreatingPrompt(true)
      const prompt = await promptService.create({
        title: newPrompt.title.trim(),
        content: newPrompt.content.trim(),
        category: newPrompt.category,
        tags: newPrompt.tags,
        user_id: user.id
      })

      setPrompts(prev => [prompt, ...prev])
      setNewPrompt({ title: "", content: "", category: "general", tags: [], folderId: "" })
      setIsNewPromptDialogOpen(false) // Close the dialog
      addToLog(`created prompt "${prompt.title}"`)
    } catch (err: any) {
      addToLog(`failed to create prompt: ${err.message}`)
    } finally {
      setIsCreatingPrompt(false)
    }
  }

  const createFolder = async () => {
    if (!user || !newFolderName.trim()) return

    try {
      setIsCreatingFolder(true)
      const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500', 'bg-yellow-500']
      const randomColor = colors[Math.floor(Math.random() * colors.length)]

      const folder = await folderService.create({
        name: newFolderName.trim(),
        color: randomColor,
        user_id: user.id
      })

      setFolders(prev => [folder, ...prev])
      setNewFolderName("")
      setIsNewFolderDialogOpen(false)
      addToLog(`created folder "${folder.name}"`)
    } catch (err: any) {
      addToLog(`failed to create folder: ${err.message}`)
    } finally {
      setIsCreatingFolder(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-0">
      <div className="w-full h-screen">
        {/* Terminal Frame */}
        <div className="bg-gray-800 h-screen overflow-hidden">
          {/* Top Navigation Bar */}
          <div className="bg-gray-700 px-6 py-3 border-b border-gray-600">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="text-green-400 font-bold text-lg terminal-glow">PROMPT MANAGER</div>
                <nav className="flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1">
                  <button className="text-green-400 hover:text-green-300 text-sm">Home</button>
                  <button className="text-green-400 hover:text-green-300 text-sm">Folders</button>
                  <button className="text-green-400 hover:text-green-300 text-sm">Settings</button>
                  <button className="text-green-400 hover:text-green-300 text-sm">Help</button>
                </nav>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-green-400 text-sm">{user?.email}</div>
                <button
                  onClick={handleSignOut}
                  className="text-green-400 hover:text-green-300 text-sm flex items-center space-x-1"
                >
                  <LogOut className="w-4 h-4" />
                  <span>Logout</span>
                </button>
                <div className="text-green-400 text-sm">{currentTime.toLocaleTimeString()}</div>
              </div>
            </div>
          </div>

          {/* Path Display */}
          <div className="bg-gray-900 px-6 py-2 border-b border-gray-700">
            <div className="flex items-center justify-between text-green-400 text-sm">
              <div className="flex items-center space-x-2">
                <Terminal className="w-4 h-4" />
                <span>user@promptmanager:</span>
                <span className="text-green-300">{currentPath}</span>
                <span className="animate-pulse">_</span>
              </div>
              <div className="flex items-center space-x-4">
                <span>Online</span>
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>

          <div className="flex h-[calc(100vh-120px)]">
            {/* Sidebar */}
            <div className="w-64 bg-gray-900 border-r border-gray-700 p-4">
              <div className="space-y-4">
                {/* Folders Section */}
                <div>
                  <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; FOLDERS</h3>
                  <div className="space-y-1">
                    <div
                      className={`flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors ${
                        selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''
                      }`}
                      onClick={() => setSelectedFolder(null)}
                    >
                      <div className="w-3 h-3 rounded bg-green-500"></div>
                      <span className="text-green-300 text-sm flex-1">All Folders</span>
                      <span className="text-green-500 text-xs">{prompts.length}</span>
                    </div>
                    {folders.map((folder) => (
                      <div
                        key={folder.id}
                        className={`flex items-center space-x-2 p-2 rounded hover:bg-gray-800 cursor-pointer transition-colors ${
                          selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''
                        }`}
                        onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                      >
                        <div className={`w-3 h-3 rounded ${folder.color}`}></div>
                        <span className="text-green-300 text-sm flex-1">{folder.name}</span>
                        <span className="text-green-500 text-xs">{folder.promptCount || 0}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Actions */}
                <div>
                  <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; ACTIONS</h3>
                  <div className="space-y-1">
                    <Dialog open={isNewPromptDialogOpen} onOpenChange={setIsNewPromptDialogOpen}>
                      <DialogTrigger asChild>
                        <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                          + New Prompt
                        </button>
                      </DialogTrigger>
                      <DialogContent className="bg-gray-800 border-gray-600 text-green-300">
                        <DialogHeader>
                          <DialogTitle className="text-green-400">Create New Prompt</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <Input
                            placeholder="Prompt title..."
                            className="bg-gray-900 border-gray-600 text-green-300"
                            value={newPrompt.title}
                            onChange={(e) => setNewPrompt(prev => ({ ...prev, title: e.target.value }))}
                          />
                          <Textarea
                            placeholder="Prompt content..."
                            className="bg-gray-900 border-gray-600 text-green-300 h-32"
                            value={newPrompt.content}
                            onChange={(e) => setNewPrompt(prev => ({ ...prev, content: e.target.value }))}
                          />
                          <Select
                            value={newPrompt.category}
                            onValueChange={(value) => setNewPrompt(prev => ({ ...prev, category: value }))}
                          >
                            <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                              <SelectItem value="general" className="text-green-300 hover:bg-gray-700">General</SelectItem>
                              <SelectItem value="development" className="text-green-300 hover:bg-gray-700">Development</SelectItem>
                              <SelectItem value="communication" className="text-green-300 hover:bg-gray-700">Communication</SelectItem>
                              <SelectItem value="analysis" className="text-green-300 hover:bg-gray-700">Analysis</SelectItem>
                              <SelectItem value="creative" className="text-green-300 hover:bg-gray-700">Creative</SelectItem>
                            </SelectContent>
                          </Select>
                          <Select
                            value={newPrompt.folderId}
                            onValueChange={(value) => setNewPrompt(prev => ({ ...prev, folderId: value }))}
                          >
                            <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                              <SelectValue placeholder="Select folder (optional)" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                              <SelectItem value="" className="text-green-300 hover:bg-gray-700">No folder</SelectItem>
                              {folders.map((folder) => (
                                <SelectItem
                                  key={folder.id}
                                  value={folder.id}
                                  className="text-green-300 hover:bg-gray-700"
                                >
                                  {folder.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            className="w-full bg-green-600 hover:bg-green-700"
                            onClick={createPrompt}
                            disabled={isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim()}
                          >
                            {isCreatingPrompt ? "Creating..." : "Create Prompt"}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Dialog open={isNewFolderDialogOpen} onOpenChange={setIsNewFolderDialogOpen}>
                      <DialogTrigger asChild>
                        <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                          + New Folder
                        </button>
                      </DialogTrigger>
                      <DialogContent className="bg-gray-800 border-gray-600 text-green-300">
                        <DialogHeader>
                          <DialogTitle className="text-green-400">Create New Folder</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <Input
                            placeholder="Folder name..."
                            className="bg-gray-900 border-gray-600 text-green-300"
                            value={newFolderName}
                            onChange={(e) => setNewFolderName(e.target.value)}
                          />
                          <Button
                            className="w-full bg-green-600 hover:bg-green-700"
                            onClick={createFolder}
                            disabled={isCreatingFolder || !newFolderName.trim()}
                          >
                            {isCreatingFolder ? "Creating..." : "Create Folder"}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                      Import/Export
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 bg-gray-800 p-6">
              {/* Search and Filter Bar */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4" />
                  <Input
                    placeholder="Search prompts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48 bg-gray-900 border-gray-600 text-green-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                    <SelectItem value="all" className="text-green-300 hover:bg-gray-700">All Categories</SelectItem>
                    <SelectItem value="development" className="text-green-300 hover:bg-gray-700">Development</SelectItem>
                    <SelectItem value="communication" className="text-green-300 hover:bg-gray-700">Communication</SelectItem>
                    <SelectItem value="analysis" className="text-green-300 hover:bg-gray-700">Analysis</SelectItem>
                    <SelectItem value="creative" className="text-green-300 hover:bg-gray-700">Creative</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Prompts Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {loadingPrompts ? (
                  <div className="col-span-full text-center text-green-400 py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4"></div>
                    Loading prompts...
                  </div>
                ) : filteredPrompts.length === 0 ? (
                  <div className="col-span-full text-center text-green-400 py-8">
                    {searchTerm || selectedCategory !== "all" ? "No prompts match your search." : "No prompts yet. Create your first prompt!"}
                  </div>
                ) : (
                  filteredPrompts.map((prompt) => (
                  <Card
                    key={prompt.id}
                    className="bg-gray-900 border-gray-700 hover:border-green-500 transition-colors"
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-green-400 text-sm font-bold">{prompt.title}</CardTitle>
                        <div className="flex items-center space-x-1">
                          {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-3 h-3 ${i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600"}`}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {prompt.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs text-green-500 border-green-600">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-green-300 text-xs mb-3 line-clamp-3">{prompt.content}</p>
                      <div className="flex items-center justify-between text-xs text-green-500">
                        <span>{new Date(prompt.created_at).toLocaleDateString()}</span>
                        {prompt.last_used && <span>Used: {new Date(prompt.last_used).toLocaleDateString()}</span>}
                      </div>
                      <div className="flex items-center space-x-2 mt-3">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyPrompt(prompt)}
                          className="text-green-400 border-green-600 hover:bg-green-900"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => togglePin(prompt.id)}
                          className="text-green-400 border-green-600 hover:bg-green-900"
                        >
                          <Pin className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-green-400 border-green-600 hover:bg-green-900"
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Status Bar */}
          <div className="bg-gray-900 px-6 py-2 border-t border-gray-700">
            <div className="flex items-center justify-between text-green-500 text-xs">
              <div className="flex items-center space-x-4">
                <span>Ready</span>
                <span>|</span>
                <span>{filteredPrompts.length} prompts</span>
                <span>|</span>
                <span>{folders.length} folders</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-3 h-3" />
                <span>Last activity: {activityLog[activityLog.length - 1]}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
